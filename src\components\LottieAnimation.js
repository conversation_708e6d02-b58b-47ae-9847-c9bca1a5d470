import React, { useRef, useEffect } from 'react';
import <PERSON><PERSON> from 'lottie-react';
import { motion, useInView } from 'framer-motion';

// Simple Lottie animation data (a bouncing ball)
const animationData = {
  "v": "5.7.4",
  "fr": 30,
  "ip": 0,
  "op": 60,
  "w": 400,
  "h": 400,
  "nm": "Bouncing Ball",
  "ddd": 0,
  "assets": [],
  "layers": [
    {
      "ddd": 0,
      "ind": 1,
      "ty": 4,
      "nm": "Ball",
      "sr": 1,
      "ks": {
        "o": {"a": 0, "k": 100},
        "r": {"a": 0, "k": 0},
        "p": {
          "a": 1,
          "k": [
            {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [200, 100, 0]},
            {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [200, 300, 0]},
            {"t": 60, "s": [200, 100, 0]}
          ]
        },
        "a": {"a": 0, "k": [0, 0, 0]},
        "s": {"a": 0, "k": [100, 100, 100]}
      },
      "ao": 0,
      "shapes": [
        {
          "ty": "gr",
          "it": [
            {
              "d": 1,
              "ty": "el",
              "s": {"a": 0, "k": [100, 100]},
              "p": {"a": 0, "k": [0, 0]},
              "nm": "Ellipse Path 1",
              "mn": "ADBE Vector Shape - Ellipse"
            },
            {
              "ty": "fl",
              "c": {"a": 0, "k": [0.2, 0.6, 1, 1]},
              "o": {"a": 0, "k": 100},
              "r": 1,
              "bm": 0,
              "nm": "Fill 1",
              "mn": "ADBE Vector Graphic - Fill"
            },
            {
              "ty": "tr",
              "p": {"a": 0, "k": [0, 0]},
              "a": {"a": 0, "k": [0, 0]},
              "s": {"a": 0, "k": [100, 100]},
              "r": {"a": 0, "k": 0},
              "o": {"a": 0, "k": 100},
              "sk": {"a": 0, "k": 0},
              "sa": {"a": 0, "k": 0},
              "nm": "Transform"
            }
          ],
          "nm": "Ellipse 1",
          "np": 3,
          "cix": 2,
          "bm": 0,
          "ix": 1,
          "mn": "ADBE Vector Group"
        }
      ],
      "ip": 0,
      "op": 60,
      "st": 0,
      "bm": 0
    }
  ],
  "markers": []
};

const LottieAnimation = () => {
  const containerRef = useRef(null);
  const lottieRef = useRef(null);
  const isInView = useInView(containerRef, { once: true, threshold: 0.3 });

  useEffect(() => {
    if (isInView && lottieRef.current) {
      // Play animation when in view
      lottieRef.current.play();
    }
  }, [isInView]);

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      ref={containerRef}
      className="flex flex-col items-center space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
    >
      <div className="relative">
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 0.8, 0.5]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        <div className="relative bg-gray-800 rounded-2xl p-8 shadow-2xl">
          <Lottie
            lottieRef={lottieRef}
            animationData={animationData}
            loop={true}
            autoplay={false}
            style={{ width: 300, height: 300 }}
            className="mx-auto"
          />
        </div>
      </div>

      <motion.div
        className="text-center max-w-md"
        initial={{ opacity: 0 }}
        animate={{ opacity: isInView ? 1 : 0 }}
        transition={{ delay: 0.5, duration: 0.6 }}
      >
        <h3 className="text-2xl font-bold mb-4 text-white">
          Lottie Animations
        </h3>
        <p className="text-gray-300 leading-relaxed">
          Lightweight, scalable animations that bring your interface to life. 
          Perfect for micro-interactions and engaging user experiences.
        </p>
      </motion.div>

      <motion.div
        className="flex space-x-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: isInView ? 1 : 0, y: isInView ? 0 : 20 }}
        transition={{ delay: 0.8, duration: 0.6 }}
      >
        <button
          onClick={() => lottieRef.current?.play()}
          className="px-6 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors"
        >
          Play
        </button>
        <button
          onClick={() => lottieRef.current?.pause()}
          className="px-6 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors"
        >
          Pause
        </button>
        <button
          onClick={() => lottieRef.current?.stop()}
          className="px-6 py-2 bg-red-600 hover:bg-red-700 rounded-lg font-medium transition-colors"
        >
          Stop
        </button>
      </motion.div>
    </motion.div>
  );
};

export default LottieAnimation;
