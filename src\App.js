import React, { useEffect } from 'react';
import Lenis from '@studio-freight/lenis';
import Hero from './sections/Hero';
import Projects from './sections/Projects';
import LottieAnimation from './components/LottieAnimation';
import SVGAnimation from './components/SVGAnimation';

function App() {
  useEffect(() => {
    // Initialize Lenis for smooth scrolling
    const lenis = new Lenis({
      duration: 1.2,
      easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
      direction: 'vertical',
      gestureDirection: 'vertical',
      smooth: true,
      mouseMultiplier: 1,
      smoothTouch: false,
      touchMultiplier: 2,
      infinite: false,
    });

    function raf(time) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }

    requestAnimationFrame(raf);

    return () => {
      lenis?.destroy();
    };
  }, []);

  return (
    <div className="App bg-black text-white min-h-screen">
      <Hero />
      <Projects />

      {/* Lottie Animation Section */}
      <section className="py-20 px-6 bg-gray-900">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-8">Interactive Animations</h2>
          <LottieAnimation />
        </div>
      </section>

      {/* SVG Animation Section */}
      <section className="py-20 px-6 bg-gray-800">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-8">SVG Animations</h2>
          <SVGAnimation />
        </div>
      </section>
    </div>
  );
}

export default App;
