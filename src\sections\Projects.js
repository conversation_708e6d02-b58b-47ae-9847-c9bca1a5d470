import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const Projects = () => {
  const sectionRef = useRef(null);
  const projectsRef = useRef([]);
  const titleRef = useRef(null);

  const projects = [
    {
      id: 1,
      title: "E-Commerce Platform",
      description: "Modern React-based shopping platform with advanced animations",
      tech: ["React", "Node.js", "MongoDB", "Framer Motion"],
      color: "from-blue-500 to-purple-600"
    },
    {
      id: 2,
      title: "Portfolio Website",
      description: "Interactive portfolio showcasing creative web development",
      tech: ["React", "GSAP", "Tailwind CSS", "Lottie"],
      color: "from-green-500 to-teal-600"
    },
    {
      id: 3,
      title: "Dashboard Analytics",
      description: "Real-time data visualization with smooth animations",
      tech: ["React", "D3.js", "Chart.js", "GSAP"],
      color: "from-orange-500 to-red-600"
    },
    {
      id: 4,
      title: "Mobile App UI",
      description: "Cross-platform mobile interface with micro-interactions",
      tech: ["React Native", "Reanimated", "Expo", "TypeScript"],
      color: "from-purple-500 to-pink-600"
    }
  ];

  useEffect(() => {
    const section = sectionRef.current;
    const projects = projectsRef.current;
    const title = titleRef.current;

    // Title animation
    gsap.fromTo(title, 
      { y: 100, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        scrollTrigger: {
          trigger: title,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );

    // Projects horizontal scroll animation
    const projectsContainer = projects[0]?.parentElement;
    if (projectsContainer) {
      gsap.to(projects, {
        xPercent: -100 * (projects.length - 1),
        ease: "none",
        scrollTrigger: {
          trigger: section,
          pin: true,
          scrub: 1,
          snap: 1 / (projects.length - 1),
          start: "top top",
          end: () => "+=" + (projectsContainer.offsetWidth - window.innerWidth)
        }
      });
    }

    // Individual project animations
    projects.forEach((project, index) => {
      gsap.fromTo(project,
        { scale: 0.8, opacity: 0.7 },
        {
          scale: 1,
          opacity: 1,
          duration: 0.5,
          scrollTrigger: {
            trigger: project,
            start: "left 80%",
            end: "left 20%",
            toggleActions: "play reverse play reverse",
            horizontal: true
          }
        }
      );
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section ref={sectionRef} className="min-h-screen bg-gray-900 overflow-hidden">
      <div className="py-20">
        <h2 
          ref={titleRef}
          className="text-5xl md:text-7xl font-bold text-center mb-20 bg-gradient-to-r from-white to-gray-400 bg-clip-text text-transparent"
        >
          Featured Projects
        </h2>

        <div className="flex w-max">
          {projects.map((project, index) => (
            <div
              key={project.id}
              ref={el => projectsRef.current[index] = el}
              className="w-screen flex items-center justify-center px-10"
            >
              <div className={`max-w-md w-full bg-gradient-to-br ${project.color} p-8 rounded-2xl shadow-2xl transform transition-all duration-300 hover:scale-105`}>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                  <h3 className="text-2xl font-bold mb-4 text-white">
                    {project.title}
                  </h3>
                  <p className="text-gray-200 mb-6 leading-relaxed">
                    {project.description}
                  </p>
                  <div className="flex flex-wrap gap-2 mb-6">
                    {project.tech.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-3 py-1 bg-white/20 rounded-full text-sm font-medium text-white"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                  <button className="w-full py-3 bg-white/20 hover:bg-white/30 rounded-lg font-semibold text-white transition-colors">
                    View Project
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Projects;
