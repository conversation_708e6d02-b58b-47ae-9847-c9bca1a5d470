#!/usr/bin/env node

"use strict";var e=require("tty"),t=require("path"),r=require("url"),n=require("fs");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=s(e),o=s(t),a=s(r),l=s(n);const u=/^media$/i,c=/\(\s*prefers-color-scheme\s*:\s*(dark|light|no-preference)\s*\)/i,h={dark:48,light:70,"no-preference":22},p=(e,t)=>`(color-index: ${h[t.toLowerCase()]})`,f={dark:48842621,light:70318723,"no-preference":22511989},d=(e,t)=>`(color: ${f[t.toLowerCase()]})`,g=e=>{const t=!("preserve"in Object(e))||e.preserve,r={};return!("mediaQuery"in Object(e))||"color-index"!==e.mediaQuery&&"color"!==e.mediaQuery?(r["color-index"]=!0,r.color=!0):r[e.mediaQuery]=!0,{postcssPlugin:"postcss-prefers-color-scheme",AtRule:e=>{if(!u.test(e.name))return;const{params:n}=e,s=n.replace(c,p),i=n.replace(c,d);let o=!1;n!==s&&r["color-index"]&&(e.cloneBefore({params:s}),o=!0),n!==i&&r.color&&(e.cloneBefore({params:i}),o=!0),!t&&o&&e.remove()}}};var m;g.postcss=!0,function(e){e.InvalidArguments="INVALID_ARGUMENTS"}(m||(m={}));var w={exports:{}};let y=i.default,v=!("NO_COLOR"in process.env||process.argv.includes("--no-color"))&&("FORCE_COLOR"in process.env||process.argv.includes("--color")||"win32"===process.platform||y.isatty(1)&&"dumb"!==process.env.TERM||"CI"in process.env),C=(e,t,r=e)=>n=>{let s=""+n,i=s.indexOf(t,e.length);return~i?e+S(s,t,r,i)+t:e+s+t},S=(e,t,r,n)=>{let s=e.substring(0,n)+r,i=e.substring(n+t.length),o=i.indexOf(t);return~o?s+S(i,t,r,o):s+i},b=(e=v)=>({isColorSupported:e,reset:e?e=>`[0m${e}[0m`:String,bold:e?C("[1m","[22m","[22m[1m"):String,dim:e?C("[2m","[22m","[22m[2m"):String,italic:e?C("[3m","[23m"):String,underline:e?C("[4m","[24m"):String,inverse:e?C("[7m","[27m"):String,hidden:e?C("[8m","[28m"):String,strikethrough:e?C("[9m","[29m"):String,black:e?C("[30m","[39m"):String,red:e?C("[31m","[39m"):String,green:e?C("[32m","[39m"):String,yellow:e?C("[33m","[39m"):String,blue:e?C("[34m","[39m"):String,magenta:e?C("[35m","[39m"):String,cyan:e?C("[36m","[39m"):String,white:e?C("[37m","[39m"):String,gray:e?C("[90m","[39m"):String,bgBlack:e?C("[40m","[49m"):String,bgRed:e?C("[41m","[49m"):String,bgGreen:e?C("[42m","[49m"):String,bgYellow:e?C("[43m","[49m"):String,bgBlue:e?C("[44m","[49m"):String,bgMagenta:e?C("[45m","[49m"):String,bgCyan:e?C("[46m","[49m"):String,bgWhite:e?C("[47m","[49m"):String});w.exports=b(),w.exports.createColors=b;const _="'".charCodeAt(0),x='"'.charCodeAt(0),O="\\".charCodeAt(0),A="/".charCodeAt(0),M="\n".charCodeAt(0),k=" ".charCodeAt(0),E="\f".charCodeAt(0),L="\t".charCodeAt(0),R="\r".charCodeAt(0),P="[".charCodeAt(0),I="]".charCodeAt(0),j="(".charCodeAt(0),N=")".charCodeAt(0),U="{".charCodeAt(0),B="}".charCodeAt(0),D=";".charCodeAt(0),F="*".charCodeAt(0),T=":".charCodeAt(0),$="@".charCodeAt(0),G=/[\t\n\f\r "#'()/;[\\\]{}]/g,z=/[\t\n\f\r !"#'():;@[\\\]{}]|\/(?=\*)/g,W=/.[\n"'(/\\]/,V=/[\da-f]/i;var J=function(e,t={}){let r,n,s,i,o,a,l,u,c,h,p=e.css.valueOf(),f=t.ignoreErrors,d=p.length,g=0,m=[],w=[];function y(t){throw e.error("Unclosed "+t,g)}return{back:function(e){w.push(e)},nextToken:function(e){if(w.length)return w.pop();if(g>=d)return;let t=!!e&&e.ignoreUnclosed;switch(r=p.charCodeAt(g),r){case M:case k:case L:case R:case E:n=g;do{n+=1,r=p.charCodeAt(n)}while(r===k||r===M||r===L||r===R||r===E);h=["space",p.slice(g,n)],g=n-1;break;case P:case I:case U:case B:case T:case D:case N:{let e=String.fromCharCode(r);h=[e,e,g];break}case j:if(u=m.length?m.pop()[1]:"",c=p.charCodeAt(g+1),"url"===u&&c!==_&&c!==x&&c!==k&&c!==M&&c!==L&&c!==E&&c!==R){n=g;do{if(a=!1,n=p.indexOf(")",n+1),-1===n){if(f||t){n=g;break}y("bracket")}for(l=n;p.charCodeAt(l-1)===O;)l-=1,a=!a}while(a);h=["brackets",p.slice(g,n+1),g,n],g=n}else n=p.indexOf(")",g+1),i=p.slice(g,n+1),-1===n||W.test(i)?h=["(","(",g]:(h=["brackets",i,g,n],g=n);break;case _:case x:s=r===_?"'":'"',n=g;do{if(a=!1,n=p.indexOf(s,n+1),-1===n){if(f||t){n=g+1;break}y("string")}for(l=n;p.charCodeAt(l-1)===O;)l-=1,a=!a}while(a);h=["string",p.slice(g,n+1),g,n],g=n;break;case $:G.lastIndex=g+1,G.test(p),n=0===G.lastIndex?p.length-1:G.lastIndex-2,h=["at-word",p.slice(g,n+1),g,n],g=n;break;case O:for(n=g,o=!0;p.charCodeAt(n+1)===O;)n+=1,o=!o;if(r=p.charCodeAt(n+1),o&&r!==A&&r!==k&&r!==M&&r!==L&&r!==R&&r!==E&&(n+=1,V.test(p.charAt(n)))){for(;V.test(p.charAt(n+1));)n+=1;p.charCodeAt(n+1)===k&&(n+=1)}h=["word",p.slice(g,n+1),g,n],g=n;break;default:r===A&&p.charCodeAt(g+1)===F?(n=p.indexOf("*/",g+2)+1,0===n&&(f||t?n=p.length:y("comment")),h=["comment",p.slice(g,n+1),g,n],g=n):(z.lastIndex=g+1,z.test(p),n=0===z.lastIndex?p.length-1:z.lastIndex-2,h=["word",p.slice(g,n+1),g,n],m.push(h),g=n)}return g++,h},endOfFile:function(){return 0===w.length&&g>=d},position:function(){return g}}};let q,Q=w.exports,Y=J;const Z={brackets:Q.cyan,"at-word":Q.cyan,comment:Q.gray,string:Q.green,class:Q.yellow,hash:Q.magenta,call:Q.cyan,"(":Q.cyan,")":Q.cyan,"{":Q.yellow,"}":Q.yellow,"[":Q.yellow,"]":Q.yellow,":":Q.yellow,";":Q.yellow};function H([e,t],r){if("word"===e){if("."===t[0])return"class";if("#"===t[0])return"hash"}if(!r.endOfFile()){let e=r.nextToken();if(r.back(e),"brackets"===e[0]||"("===e[0])return"call"}return e}function K(e){let t=Y(new q(e),{ignoreErrors:!0}),r="";for(;!t.endOfFile();){let e=t.nextToken(),n=Z[H(e,t)];r+=n?e[1].split(/\r?\n/).map((e=>n(e))).join("\n"):e[1]}return r}K.registerInput=function(e){q=e};var X=K;let ee=w.exports,te=X;class re extends Error{constructor(e,t,r,n,s,i){super(e),this.name="CssSyntaxError",this.reason=e,s&&(this.file=s),n&&(this.source=n),i&&(this.plugin=i),void 0!==t&&void 0!==r&&("number"==typeof t?(this.line=t,this.column=r):(this.line=t.line,this.column=t.column,this.endLine=r.line,this.endColumn=r.column)),this.setMessage(),Error.captureStackTrace&&Error.captureStackTrace(this,re)}setMessage(){this.message=this.plugin?this.plugin+": ":"",this.message+=this.file?this.file:"<css input>",void 0!==this.line&&(this.message+=":"+this.line+":"+this.column),this.message+=": "+this.reason}showSourceCode(e){if(!this.source)return"";let t=this.source;null==e&&(e=ee.isColorSupported),te&&e&&(t=te(t));let r,n,s=t.split(/\r?\n/),i=Math.max(this.line-3,0),o=Math.min(this.line+2,s.length),a=String(o).length;if(e){let{bold:e,red:t,gray:s}=ee.createColors(!0);r=r=>e(t(r)),n=e=>s(e)}else r=n=e=>e;return s.slice(i,o).map(((e,t)=>{let s=i+1+t,o=" "+(" "+s).slice(-a)+" | ";if(s===this.line){let t=n(o.replace(/\d/g," "))+e.slice(0,this.column-1).replace(/[^\t]/g," ");return r(">")+n(o)+e+"\n "+t+r("^")}return" "+n(o)+e})).join("\n")}toString(){let e=this.showSourceCode();return e&&(e="\n\n"+e+"\n"),this.name+": "+this.message+e}}var ne=re;re.default=re;var se={};se.isClean=Symbol("isClean"),se.my=Symbol("my");const ie={colon:": ",indent:"    ",beforeDecl:"\n",beforeRule:"\n",beforeOpen:" ",beforeClose:"\n",beforeComment:"\n",after:"\n",emptyBody:"",commentLeft:" ",commentRight:" ",semicolon:!1};class oe{constructor(e){this.builder=e}stringify(e,t){if(!this[e.type])throw new Error("Unknown AST node type "+e.type+". Maybe you need to change PostCSS stringifier.");this[e.type](e,t)}document(e){this.body(e)}root(e){this.body(e),e.raws.after&&this.builder(e.raws.after)}comment(e){let t=this.raw(e,"left","commentLeft"),r=this.raw(e,"right","commentRight");this.builder("/*"+t+e.text+r+"*/",e)}decl(e,t){let r=this.raw(e,"between","colon"),n=e.prop+r+this.rawValue(e,"value");e.important&&(n+=e.raws.important||" !important"),t&&(n+=";"),this.builder(n,e)}rule(e){this.block(e,this.rawValue(e,"selector")),e.raws.ownSemicolon&&this.builder(e.raws.ownSemicolon,e,"end")}atrule(e,t){let r="@"+e.name,n=e.params?this.rawValue(e,"params"):"";if(void 0!==e.raws.afterName?r+=e.raws.afterName:n&&(r+=" "),e.nodes)this.block(e,r+n);else{let s=(e.raws.between||"")+(t?";":"");this.builder(r+n+s,e)}}body(e){let t=e.nodes.length-1;for(;t>0&&"comment"===e.nodes[t].type;)t-=1;let r=this.raw(e,"semicolon");for(let n=0;n<e.nodes.length;n++){let s=e.nodes[n],i=this.raw(s,"before");i&&this.builder(i),this.stringify(s,t!==n||r)}}block(e,t){let r,n=this.raw(e,"between","beforeOpen");this.builder(t+n+"{",e,"start"),e.nodes&&e.nodes.length?(this.body(e),r=this.raw(e,"after")):r=this.raw(e,"after","emptyBody"),r&&this.builder(r),this.builder("}",e,"end")}raw(e,t,r){let n;if(r||(r=t),t&&(n=e.raws[t],void 0!==n))return n;let s=e.parent;if("before"===r){if(!s||"root"===s.type&&s.first===e)return"";if(s&&"document"===s.type)return""}if(!s)return ie[r];let i=e.root();if(i.rawCache||(i.rawCache={}),void 0!==i.rawCache[r])return i.rawCache[r];if("before"===r||"after"===r)return this.beforeAfter(e,r);{let s="raw"+((o=r)[0].toUpperCase()+o.slice(1));this[s]?n=this[s](i,e):i.walk((e=>{if(n=e.raws[t],void 0!==n)return!1}))}var o;return void 0===n&&(n=ie[r]),i.rawCache[r]=n,n}rawSemicolon(e){let t;return e.walk((e=>{if(e.nodes&&e.nodes.length&&"decl"===e.last.type&&(t=e.raws.semicolon,void 0!==t))return!1})),t}rawEmptyBody(e){let t;return e.walk((e=>{if(e.nodes&&0===e.nodes.length&&(t=e.raws.after,void 0!==t))return!1})),t}rawIndent(e){if(e.raws.indent)return e.raws.indent;let t;return e.walk((r=>{let n=r.parent;if(n&&n!==e&&n.parent&&n.parent===e&&void 0!==r.raws.before){let e=r.raws.before.split("\n");return t=e[e.length-1],t=t.replace(/\S/g,""),!1}})),t}rawBeforeComment(e,t){let r;return e.walkComments((e=>{if(void 0!==e.raws.before)return r=e.raws.before,r.includes("\n")&&(r=r.replace(/[^\n]+$/,"")),!1})),void 0===r?r=this.raw(t,null,"beforeDecl"):r&&(r=r.replace(/\S/g,"")),r}rawBeforeDecl(e,t){let r;return e.walkDecls((e=>{if(void 0!==e.raws.before)return r=e.raws.before,r.includes("\n")&&(r=r.replace(/[^\n]+$/,"")),!1})),void 0===r?r=this.raw(t,null,"beforeRule"):r&&(r=r.replace(/\S/g,"")),r}rawBeforeRule(e){let t;return e.walk((r=>{if(r.nodes&&(r.parent!==e||e.first!==r)&&void 0!==r.raws.before)return t=r.raws.before,t.includes("\n")&&(t=t.replace(/[^\n]+$/,"")),!1})),t&&(t=t.replace(/\S/g,"")),t}rawBeforeClose(e){let t;return e.walk((e=>{if(e.nodes&&e.nodes.length>0&&void 0!==e.raws.after)return t=e.raws.after,t.includes("\n")&&(t=t.replace(/[^\n]+$/,"")),!1})),t&&(t=t.replace(/\S/g,"")),t}rawBeforeOpen(e){let t;return e.walk((e=>{if("decl"!==e.type&&(t=e.raws.between,void 0!==t))return!1})),t}rawColon(e){let t;return e.walkDecls((e=>{if(void 0!==e.raws.between)return t=e.raws.between.replace(/[^\s:]/g,""),!1})),t}beforeAfter(e,t){let r;r="decl"===e.type?this.raw(e,null,"beforeDecl"):"comment"===e.type?this.raw(e,null,"beforeComment"):"before"===t?this.raw(e,null,"beforeRule"):this.raw(e,null,"beforeClose");let n=e.parent,s=0;for(;n&&"root"!==n.type;)s+=1,n=n.parent;if(r.includes("\n")){let t=this.raw(e,null,"indent");if(t.length)for(let e=0;e<s;e++)r+=t}return r}rawValue(e,t){let r=e[t],n=e.raws[t];return n&&n.value===r?n.raw:r}}var ae=oe;oe.default=oe;let le=ae;function ue(e,t){new le(t).stringify(e)}var ce=ue;ue.default=ue;let{isClean:he,my:pe}=se,fe=ne,de=ae,ge=ce;function me(e,t){let r=new e.constructor;for(let n in e){if(!Object.prototype.hasOwnProperty.call(e,n))continue;if("proxyCache"===n)continue;let s=e[n],i=typeof s;"parent"===n&&"object"===i?t&&(r[n]=t):"source"===n?r[n]=s:Array.isArray(s)?r[n]=s.map((e=>me(e,r))):("object"===i&&null!==s&&(s=me(s)),r[n]=s)}return r}class we{constructor(e={}){this.raws={},this[he]=!1,this[pe]=!0;for(let t in e)if("nodes"===t){this.nodes=[];for(let r of e[t])"function"==typeof r.clone?this.append(r.clone()):this.append(r)}else this[t]=e[t]}error(e,t={}){if(this.source){let{start:r,end:n}=this.rangeBy(t);return this.source.input.error(e,{line:r.line,column:r.column},{line:n.line,column:n.column},t)}return new fe(e)}warn(e,t,r){let n={node:this};for(let e in r)n[e]=r[e];return e.warn(t,n)}remove(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this}toString(e=ge){e.stringify&&(e=e.stringify);let t="";return e(this,(e=>{t+=e})),t}assign(e={}){for(let t in e)this[t]=e[t];return this}clone(e={}){let t=me(this);for(let r in e)t[r]=e[r];return t}cloneBefore(e={}){let t=this.clone(e);return this.parent.insertBefore(this,t),t}cloneAfter(e={}){let t=this.clone(e);return this.parent.insertAfter(this,t),t}replaceWith(...e){if(this.parent){let t=this,r=!1;for(let n of e)n===this?r=!0:r?(this.parent.insertAfter(t,n),t=n):this.parent.insertBefore(t,n);r||this.remove()}return this}next(){if(!this.parent)return;let e=this.parent.index(this);return this.parent.nodes[e+1]}prev(){if(!this.parent)return;let e=this.parent.index(this);return this.parent.nodes[e-1]}before(e){return this.parent.insertBefore(this,e),this}after(e){return this.parent.insertAfter(this,e),this}root(){let e=this;for(;e.parent&&"document"!==e.parent.type;)e=e.parent;return e}raw(e,t){return(new de).raw(this,e,t)}cleanRaws(e){delete this.raws.before,delete this.raws.after,e||delete this.raws.between}toJSON(e,t){let r={},n=null==t;t=t||new Map;let s=0;for(let e in this){if(!Object.prototype.hasOwnProperty.call(this,e))continue;if("parent"===e||"proxyCache"===e)continue;let n=this[e];if(Array.isArray(n))r[e]=n.map((e=>"object"==typeof e&&e.toJSON?e.toJSON(null,t):e));else if("object"==typeof n&&n.toJSON)r[e]=n.toJSON(null,t);else if("source"===e){let i=t.get(n.input);null==i&&(i=s,t.set(n.input,s),s++),r[e]={inputId:i,start:n.start,end:n.end}}else r[e]=n}return n&&(r.inputs=[...t.keys()].map((e=>e.toJSON()))),r}positionInside(e){let t=this.toString(),r=this.source.start.column,n=this.source.start.line;for(let s=0;s<e;s++)"\n"===t[s]?(r=1,n+=1):r+=1;return{line:n,column:r}}positionBy(e){let t=this.source.start;if(e.index)t=this.positionInside(e.index);else if(e.word){let r=this.toString().indexOf(e.word);-1!==r&&(t=this.positionInside(r))}return t}rangeBy(e){let t={line:this.source.start.line,column:this.source.start.column},r=this.source.end?{line:this.source.end.line,column:this.source.end.column+1}:{line:t.line,column:t.column+1};if(e.word){let n=this.toString().indexOf(e.word);-1!==n&&(t=this.positionInside(n),r=this.positionInside(n+e.word.length))}else e.start?t={line:e.start.line,column:e.start.column}:e.index&&(t=this.positionInside(e.index)),e.end?r={line:e.end.line,column:e.end.column}:e.endIndex?r=this.positionInside(e.endIndex):e.index&&(r=this.positionInside(e.index+1));return(r.line<t.line||r.line===t.line&&r.column<=t.column)&&(r={line:t.line,column:t.column+1}),{start:t,end:r}}getProxyProcessor(){return{set:(e,t,r)=>(e[t]===r||(e[t]=r,"prop"!==t&&"value"!==t&&"name"!==t&&"params"!==t&&"important"!==t&&"text"!==t||e.markDirty()),!0),get:(e,t)=>"proxyOf"===t?e:"root"===t?()=>e.root().toProxy():e[t]}}toProxy(){return this.proxyCache||(this.proxyCache=new Proxy(this,this.getProxyProcessor())),this.proxyCache}addToError(e){if(e.postcssNode=this,e.stack&&this.source&&/\n\s{4}at /.test(e.stack)){let t=this.source;e.stack=e.stack.replace(/\n\s{4}at /,`$&${t.input.from}:${t.start.line}:${t.start.column}$&`)}return e}markDirty(){if(this[he]){this[he]=!1;let e=this;for(;e=e.parent;)e[he]=!1}}get proxyOf(){return this}}var ye=we;we.default=we;let ve=ye;class Ce extends ve{constructor(e){e&&void 0!==e.value&&"string"!=typeof e.value&&(e={...e,value:String(e.value)}),super(e),this.type="decl"}get variable(){return this.prop.startsWith("--")||"$"===this.prop[0]}}var Se=Ce;Ce.default=Ce;var be={},_e={},xe={},Oe={},Ae="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");Oe.encode=function(e){if(0<=e&&e<Ae.length)return Ae[e];throw new TypeError("Must be between 0 and 63: "+e)},Oe.decode=function(e){return 65<=e&&e<=90?e-65:97<=e&&e<=122?e-97+26:48<=e&&e<=57?e-48+52:43==e?62:47==e?63:-1};var Me=Oe;xe.encode=function(e){var t,r="",n=function(e){return e<0?1+(-e<<1):0+(e<<1)}(e);do{t=31&n,(n>>>=5)>0&&(t|=32),r+=Me.encode(t)}while(n>0);return r},xe.decode=function(e,t,r){var n,s,i,o,a=e.length,l=0,u=0;do{if(t>=a)throw new Error("Expected more digits in base 64 VLQ value.");if(-1===(s=Me.decode(e.charCodeAt(t++))))throw new Error("Invalid base64 digit: "+e.charAt(t-1));n=!!(32&s),l+=(s&=31)<<u,u+=5}while(n);r.value=(o=(i=l)>>1,1==(1&i)?-o:o),r.rest=t};var ke={};!function(e){e.getArg=function(e,t,r){if(t in e)return e[t];if(3===arguments.length)return r;throw new Error('"'+t+'" is a required argument.')};var t=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,r=/^data:.+\,.+$/;function n(e){var r=e.match(t);return r?{scheme:r[1],auth:r[2],host:r[3],port:r[4],path:r[5]}:null}function s(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}e.urlParse=n,e.urlGenerate=s;var i,o,a=(i=function(t){var r=t,i=n(t);if(i){if(!i.path)return t;r=i.path}for(var o=e.isAbsolute(r),a=[],l=0,u=0;;){if(l=u,-1===(u=r.indexOf("/",l))){a.push(r.slice(l));break}for(a.push(r.slice(l,u));u<r.length&&"/"===r[u];)u++}var c,h=0;for(u=a.length-1;u>=0;u--)"."===(c=a[u])?a.splice(u,1):".."===c?h++:h>0&&(""===c?(a.splice(u+1,h),h=0):(a.splice(u,2),h--));return""===(r=a.join("/"))&&(r=o?"/":"."),i?(i.path=r,s(i)):r},o=[],function(e){for(var t=0;t<o.length;t++)if(o[t].input===e){var r=o[0];return o[0]=o[t],o[t]=r,o[0].result}var n=i(e);return o.unshift({input:e,result:n}),o.length>32&&o.pop(),n});function l(e,t){""===e&&(e="."),""===t&&(t=".");var i=n(t),o=n(e);if(o&&(e=o.path||"/"),i&&!i.scheme)return o&&(i.scheme=o.scheme),s(i);if(i||t.match(r))return t;if(o&&!o.host&&!o.path)return o.host=t,s(o);var l="/"===t.charAt(0)?t:a(e.replace(/\/+$/,"")+"/"+t);return o?(o.path=l,s(o)):l}e.normalize=a,e.join=l,e.isAbsolute=function(e){return"/"===e.charAt(0)||t.test(e)},e.relative=function(e,t){""===e&&(e="."),e=e.replace(/\/$/,"");for(var r=0;0!==t.indexOf(e+"/");){var n=e.lastIndexOf("/");if(n<0)return t;if((e=e.slice(0,n)).match(/^([^\/]+:\/)?\/*$/))return t;++r}return Array(r+1).join("../")+t.substr(e.length+1)};var u=!("__proto__"in Object.create(null));function c(e){return e}function h(e){if(!e)return!1;var t=e.length;if(t<9)return!1;if(95!==e.charCodeAt(t-1)||95!==e.charCodeAt(t-2)||111!==e.charCodeAt(t-3)||116!==e.charCodeAt(t-4)||111!==e.charCodeAt(t-5)||114!==e.charCodeAt(t-6)||112!==e.charCodeAt(t-7)||95!==e.charCodeAt(t-8)||95!==e.charCodeAt(t-9))return!1;for(var r=t-10;r>=0;r--)if(36!==e.charCodeAt(r))return!1;return!0}function p(e,t){return e===t?0:null===e?1:null===t?-1:e>t?1:-1}e.toSetString=u?c:function(e){return h(e)?"$"+e:e},e.fromSetString=u?c:function(e){return h(e)?e.slice(1):e},e.compareByOriginalPositions=function(e,t,r){var n=p(e.source,t.source);return 0!==n||0!==(n=e.originalLine-t.originalLine)||0!==(n=e.originalColumn-t.originalColumn)||r||0!==(n=e.generatedColumn-t.generatedColumn)||0!==(n=e.generatedLine-t.generatedLine)?n:p(e.name,t.name)},e.compareByOriginalPositionsNoSource=function(e,t,r){var n;return 0!==(n=e.originalLine-t.originalLine)||0!==(n=e.originalColumn-t.originalColumn)||r||0!==(n=e.generatedColumn-t.generatedColumn)||0!==(n=e.generatedLine-t.generatedLine)?n:p(e.name,t.name)},e.compareByGeneratedPositionsDeflated=function(e,t,r){var n=e.generatedLine-t.generatedLine;return 0!==n||0!==(n=e.generatedColumn-t.generatedColumn)||r||0!==(n=p(e.source,t.source))||0!==(n=e.originalLine-t.originalLine)||0!==(n=e.originalColumn-t.originalColumn)?n:p(e.name,t.name)},e.compareByGeneratedPositionsDeflatedNoLine=function(e,t,r){var n=e.generatedColumn-t.generatedColumn;return 0!==n||r||0!==(n=p(e.source,t.source))||0!==(n=e.originalLine-t.originalLine)||0!==(n=e.originalColumn-t.originalColumn)?n:p(e.name,t.name)},e.compareByGeneratedPositionsInflated=function(e,t){var r=e.generatedLine-t.generatedLine;return 0!==r||0!==(r=e.generatedColumn-t.generatedColumn)||0!==(r=p(e.source,t.source))||0!==(r=e.originalLine-t.originalLine)||0!==(r=e.originalColumn-t.originalColumn)?r:p(e.name,t.name)},e.parseSourceMapInput=function(e){return JSON.parse(e.replace(/^\)]}'[^\n]*\n/,""))},e.computeSourceURL=function(e,t,r){if(t=t||"",e&&("/"!==e[e.length-1]&&"/"!==t[0]&&(e+="/"),t=e+t),r){var i=n(r);if(!i)throw new Error("sourceMapURL could not be parsed");if(i.path){var o=i.path.lastIndexOf("/");o>=0&&(i.path=i.path.substring(0,o+1))}t=l(s(i),t)}return a(t)}}(ke);var Ee={},Le=ke,Re=Object.prototype.hasOwnProperty,Pe="undefined"!=typeof Map;function Ie(){this._array=[],this._set=Pe?new Map:Object.create(null)}Ie.fromArray=function(e,t){for(var r=new Ie,n=0,s=e.length;n<s;n++)r.add(e[n],t);return r},Ie.prototype.size=function(){return Pe?this._set.size:Object.getOwnPropertyNames(this._set).length},Ie.prototype.add=function(e,t){var r=Pe?e:Le.toSetString(e),n=Pe?this.has(e):Re.call(this._set,r),s=this._array.length;n&&!t||this._array.push(e),n||(Pe?this._set.set(e,s):this._set[r]=s)},Ie.prototype.has=function(e){if(Pe)return this._set.has(e);var t=Le.toSetString(e);return Re.call(this._set,t)},Ie.prototype.indexOf=function(e){if(Pe){var t=this._set.get(e);if(t>=0)return t}else{var r=Le.toSetString(e);if(Re.call(this._set,r))return this._set[r]}throw new Error('"'+e+'" is not in the set.')},Ie.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},Ie.prototype.toArray=function(){return this._array.slice()},Ee.ArraySet=Ie;var je={},Ne=ke;function Ue(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}Ue.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)},Ue.prototype.add=function(e){var t,r,n,s,i,o;t=this._last,r=e,n=t.generatedLine,s=r.generatedLine,i=t.generatedColumn,o=r.generatedColumn,s>n||s==n&&o>=i||Ne.compareByGeneratedPositionsInflated(t,r)<=0?(this._last=e,this._array.push(e)):(this._sorted=!1,this._array.push(e))},Ue.prototype.toArray=function(){return this._sorted||(this._array.sort(Ne.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},je.MappingList=Ue;var Be=xe,De=ke,Fe=Ee.ArraySet,Te=je.MappingList;function $e(e){e||(e={}),this._file=De.getArg(e,"file",null),this._sourceRoot=De.getArg(e,"sourceRoot",null),this._skipValidation=De.getArg(e,"skipValidation",!1),this._sources=new Fe,this._names=new Fe,this._mappings=new Te,this._sourcesContents=null}$e.prototype._version=3,$e.fromSourceMap=function(e){var t=e.sourceRoot,r=new $e({file:e.file,sourceRoot:t});return e.eachMapping((function(e){var n={generated:{line:e.generatedLine,column:e.generatedColumn}};null!=e.source&&(n.source=e.source,null!=t&&(n.source=De.relative(t,n.source)),n.original={line:e.originalLine,column:e.originalColumn},null!=e.name&&(n.name=e.name)),r.addMapping(n)})),e.sources.forEach((function(n){var s=n;null!==t&&(s=De.relative(t,n)),r._sources.has(s)||r._sources.add(s);var i=e.sourceContentFor(n);null!=i&&r.setSourceContent(n,i)})),r},$e.prototype.addMapping=function(e){var t=De.getArg(e,"generated"),r=De.getArg(e,"original",null),n=De.getArg(e,"source",null),s=De.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,r,n,s),null!=n&&(n=String(n),this._sources.has(n)||this._sources.add(n)),null!=s&&(s=String(s),this._names.has(s)||this._names.add(s)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:null!=r&&r.line,originalColumn:null!=r&&r.column,source:n,name:s})},$e.prototype.setSourceContent=function(e,t){var r=e;null!=this._sourceRoot&&(r=De.relative(this._sourceRoot,r)),null!=t?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[De.toSetString(r)]=t):this._sourcesContents&&(delete this._sourcesContents[De.toSetString(r)],0===Object.keys(this._sourcesContents).length&&(this._sourcesContents=null))},$e.prototype.applySourceMap=function(e,t,r){var n=t;if(null==t){if(null==e.file)throw new Error('SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map\'s "file" property. Both were omitted.');n=e.file}var s=this._sourceRoot;null!=s&&(n=De.relative(s,n));var i=new Fe,o=new Fe;this._mappings.unsortedForEach((function(t){if(t.source===n&&null!=t.originalLine){var a=e.originalPositionFor({line:t.originalLine,column:t.originalColumn});null!=a.source&&(t.source=a.source,null!=r&&(t.source=De.join(r,t.source)),null!=s&&(t.source=De.relative(s,t.source)),t.originalLine=a.line,t.originalColumn=a.column,null!=a.name&&(t.name=a.name))}var l=t.source;null==l||i.has(l)||i.add(l);var u=t.name;null==u||o.has(u)||o.add(u)}),this),this._sources=i,this._names=o,e.sources.forEach((function(t){var n=e.sourceContentFor(t);null!=n&&(null!=r&&(t=De.join(r,t)),null!=s&&(t=De.relative(s,t)),this.setSourceContent(t,n))}),this)},$e.prototype._validateMapping=function(e,t,r,n){if(t&&"number"!=typeof t.line&&"number"!=typeof t.column)throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(e&&"line"in e&&"column"in e&&e.line>0&&e.column>=0)||t||r||n)&&!(e&&"line"in e&&"column"in e&&t&&"line"in t&&"column"in t&&e.line>0&&e.column>=0&&t.line>0&&t.column>=0&&r))throw new Error("Invalid mapping: "+JSON.stringify({generated:e,source:r,original:t,name:n}))},$e.prototype._serializeMappings=function(){for(var e,t,r,n,s=0,i=1,o=0,a=0,l=0,u=0,c="",h=this._mappings.toArray(),p=0,f=h.length;p<f;p++){if(e="",(t=h[p]).generatedLine!==i)for(s=0;t.generatedLine!==i;)e+=";",i++;else if(p>0){if(!De.compareByGeneratedPositionsInflated(t,h[p-1]))continue;e+=","}e+=Be.encode(t.generatedColumn-s),s=t.generatedColumn,null!=t.source&&(n=this._sources.indexOf(t.source),e+=Be.encode(n-u),u=n,e+=Be.encode(t.originalLine-1-a),a=t.originalLine-1,e+=Be.encode(t.originalColumn-o),o=t.originalColumn,null!=t.name&&(r=this._names.indexOf(t.name),e+=Be.encode(r-l),l=r)),c+=e}return c},$e.prototype._generateSourcesContent=function(e,t){return e.map((function(e){if(!this._sourcesContents)return null;null!=t&&(e=De.relative(t,e));var r=De.toSetString(e);return Object.prototype.hasOwnProperty.call(this._sourcesContents,r)?this._sourcesContents[r]:null}),this)},$e.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return null!=this._file&&(e.file=this._file),null!=this._sourceRoot&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e},$e.prototype.toString=function(){return JSON.stringify(this.toJSON())},_e.SourceMapGenerator=$e;var Ge={},ze={};!function(e){function t(r,n,s,i,o,a){var l=Math.floor((n-r)/2)+r,u=o(s,i[l],!0);return 0===u?l:u>0?n-l>1?t(l,n,s,i,o,a):a==e.LEAST_UPPER_BOUND?n<i.length?n:-1:l:l-r>1?t(r,l,s,i,o,a):a==e.LEAST_UPPER_BOUND?l:r<0?-1:r}e.GREATEST_LOWER_BOUND=1,e.LEAST_UPPER_BOUND=2,e.search=function(r,n,s,i){if(0===n.length)return-1;var o=t(-1,n.length,r,n,s,i||e.GREATEST_LOWER_BOUND);if(o<0)return-1;for(;o-1>=0&&0===s(n[o],n[o-1],!0);)--o;return o}}(ze);var We={};function Ve(e){function t(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}return function e(r,n,s,i){if(s<i){var o=s-1;t(r,(c=s,h=i,Math.round(c+Math.random()*(h-c))),i);for(var a=r[i],l=s;l<i;l++)n(r[l],a,!1)<=0&&t(r,o+=1,l);t(r,o+1,l);var u=o+1;e(r,n,s,u-1),e(r,n,u+1,i)}var c,h}}let Je=new WeakMap;We.quickSort=function(e,t,r=0){let n=Je.get(t);void 0===n&&(n=function(e){let t=Ve.toString();return new Function(`return ${t}`)()(e)}(t),Je.set(t,n)),n(e,t,r,e.length-1)};var qe=ke,Qe=ze,Ye=Ee.ArraySet,Ze=xe,He=We.quickSort;function Ke(e,t){var r=e;return"string"==typeof e&&(r=qe.parseSourceMapInput(e)),null!=r.sections?new nt(r,t):new Xe(r,t)}function Xe(e,t){var r=e;"string"==typeof e&&(r=qe.parseSourceMapInput(e));var n=qe.getArg(r,"version"),s=qe.getArg(r,"sources"),i=qe.getArg(r,"names",[]),o=qe.getArg(r,"sourceRoot",null),a=qe.getArg(r,"sourcesContent",null),l=qe.getArg(r,"mappings"),u=qe.getArg(r,"file",null);if(n!=this._version)throw new Error("Unsupported version: "+n);o&&(o=qe.normalize(o)),s=s.map(String).map(qe.normalize).map((function(e){return o&&qe.isAbsolute(o)&&qe.isAbsolute(e)?qe.relative(o,e):e})),this._names=Ye.fromArray(i.map(String),!0),this._sources=Ye.fromArray(s,!0),this._absoluteSources=this._sources.toArray().map((function(e){return qe.computeSourceURL(o,e,t)})),this.sourceRoot=o,this.sourcesContent=a,this._mappings=l,this._sourceMapURL=t,this.file=u}function et(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}Ke.fromSourceMap=function(e,t){return Xe.fromSourceMap(e,t)},Ke.prototype._version=3,Ke.prototype.__generatedMappings=null,Object.defineProperty(Ke.prototype,"_generatedMappings",{configurable:!0,enumerable:!0,get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),Ke.prototype.__originalMappings=null,Object.defineProperty(Ke.prototype,"_originalMappings",{configurable:!0,enumerable:!0,get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),Ke.prototype._charIsMappingSeparator=function(e,t){var r=e.charAt(t);return";"===r||","===r},Ke.prototype._parseMappings=function(e,t){throw new Error("Subclasses must implement _parseMappings")},Ke.GENERATED_ORDER=1,Ke.ORIGINAL_ORDER=2,Ke.GREATEST_LOWER_BOUND=1,Ke.LEAST_UPPER_BOUND=2,Ke.prototype.eachMapping=function(e,t,r){var n,s=t||null;switch(r||Ke.GENERATED_ORDER){case Ke.GENERATED_ORDER:n=this._generatedMappings;break;case Ke.ORIGINAL_ORDER:n=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}for(var i=this.sourceRoot,o=e.bind(s),a=this._names,l=this._sources,u=this._sourceMapURL,c=0,h=n.length;c<h;c++){var p=n[c],f=null===p.source?null:l.at(p.source);o({source:f=qe.computeSourceURL(i,f,u),generatedLine:p.generatedLine,generatedColumn:p.generatedColumn,originalLine:p.originalLine,originalColumn:p.originalColumn,name:null===p.name?null:a.at(p.name)})}},Ke.prototype.allGeneratedPositionsFor=function(e){var t=qe.getArg(e,"line"),r={source:qe.getArg(e,"source"),originalLine:t,originalColumn:qe.getArg(e,"column",0)};if(r.source=this._findSourceIndex(r.source),r.source<0)return[];var n=[],s=this._findMapping(r,this._originalMappings,"originalLine","originalColumn",qe.compareByOriginalPositions,Qe.LEAST_UPPER_BOUND);if(s>=0){var i=this._originalMappings[s];if(void 0===e.column)for(var o=i.originalLine;i&&i.originalLine===o;)n.push({line:qe.getArg(i,"generatedLine",null),column:qe.getArg(i,"generatedColumn",null),lastColumn:qe.getArg(i,"lastGeneratedColumn",null)}),i=this._originalMappings[++s];else for(var a=i.originalColumn;i&&i.originalLine===t&&i.originalColumn==a;)n.push({line:qe.getArg(i,"generatedLine",null),column:qe.getArg(i,"generatedColumn",null),lastColumn:qe.getArg(i,"lastGeneratedColumn",null)}),i=this._originalMappings[++s]}return n},Ge.SourceMapConsumer=Ke,Xe.prototype=Object.create(Ke.prototype),Xe.prototype.consumer=Ke,Xe.prototype._findSourceIndex=function(e){var t,r=e;if(null!=this.sourceRoot&&(r=qe.relative(this.sourceRoot,r)),this._sources.has(r))return this._sources.indexOf(r);for(t=0;t<this._absoluteSources.length;++t)if(this._absoluteSources[t]==e)return t;return-1},Xe.fromSourceMap=function(e,t){var r=Object.create(Xe.prototype),n=r._names=Ye.fromArray(e._names.toArray(),!0),s=r._sources=Ye.fromArray(e._sources.toArray(),!0);r.sourceRoot=e._sourceRoot,r.sourcesContent=e._generateSourcesContent(r._sources.toArray(),r.sourceRoot),r.file=e._file,r._sourceMapURL=t,r._absoluteSources=r._sources.toArray().map((function(e){return qe.computeSourceURL(r.sourceRoot,e,t)}));for(var i=e._mappings.toArray().slice(),o=r.__generatedMappings=[],a=r.__originalMappings=[],l=0,u=i.length;l<u;l++){var c=i[l],h=new et;h.generatedLine=c.generatedLine,h.generatedColumn=c.generatedColumn,c.source&&(h.source=s.indexOf(c.source),h.originalLine=c.originalLine,h.originalColumn=c.originalColumn,c.name&&(h.name=n.indexOf(c.name)),a.push(h)),o.push(h)}return He(r.__originalMappings,qe.compareByOriginalPositions),r},Xe.prototype._version=3,Object.defineProperty(Xe.prototype,"sources",{get:function(){return this._absoluteSources.slice()}});const tt=qe.compareByGeneratedPositionsDeflatedNoLine;function rt(e,t){let r=e.length,n=e.length-t;if(!(n<=1))if(2==n){let r=e[t],n=e[t+1];tt(r,n)>0&&(e[t]=n,e[t+1]=r)}else if(n<20)for(let n=t;n<r;n++)for(let r=n;r>t;r--){let t=e[r-1],n=e[r];if(tt(t,n)<=0)break;e[r-1]=n,e[r]=t}else He(e,tt,t)}function nt(e,t){var r=e;"string"==typeof e&&(r=qe.parseSourceMapInput(e));var n=qe.getArg(r,"version"),s=qe.getArg(r,"sections");if(n!=this._version)throw new Error("Unsupported version: "+n);this._sources=new Ye,this._names=new Ye;var i={line:-1,column:0};this._sections=s.map((function(e){if(e.url)throw new Error("Support for url field in sections not implemented.");var r=qe.getArg(e,"offset"),n=qe.getArg(r,"line"),s=qe.getArg(r,"column");if(n<i.line||n===i.line&&s<i.column)throw new Error("Section offsets must be ordered and non-overlapping.");return i=r,{generatedOffset:{generatedLine:n+1,generatedColumn:s+1},consumer:new Ke(qe.getArg(e,"map"),t)}}))}Xe.prototype._parseMappings=function(e,t){var r,n,s,i,o=1,a=0,l=0,u=0,c=0,h=0,p=e.length,f=0,d={},g=[],m=[];let w=0;for(;f<p;)if(";"===e.charAt(f))o++,f++,a=0,rt(m,w),w=m.length;else if(","===e.charAt(f))f++;else{for((r=new et).generatedLine=o,s=f;s<p&&!this._charIsMappingSeparator(e,s);s++);for(e.slice(f,s),n=[];f<s;)Ze.decode(e,f,d),i=d.value,f=d.rest,n.push(i);if(2===n.length)throw new Error("Found a source, but no line and column");if(3===n.length)throw new Error("Found a source and line, but no column");if(r.generatedColumn=a+n[0],a=r.generatedColumn,n.length>1&&(r.source=c+n[1],c+=n[1],r.originalLine=l+n[2],l=r.originalLine,r.originalLine+=1,r.originalColumn=u+n[3],u=r.originalColumn,n.length>4&&(r.name=h+n[4],h+=n[4])),m.push(r),"number"==typeof r.originalLine){let e=r.source;for(;g.length<=e;)g.push(null);null===g[e]&&(g[e]=[]),g[e].push(r)}}rt(m,w),this.__generatedMappings=m;for(var y=0;y<g.length;y++)null!=g[y]&&He(g[y],qe.compareByOriginalPositionsNoSource);this.__originalMappings=[].concat(...g)},Xe.prototype._findMapping=function(e,t,r,n,s,i){if(e[r]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+e[r]);if(e[n]<0)throw new TypeError("Column must be greater than or equal to 0, got "+e[n]);return Qe.search(e,t,s,i)},Xe.prototype.computeColumnSpans=function(){for(var e=0;e<this._generatedMappings.length;++e){var t=this._generatedMappings[e];if(e+1<this._generatedMappings.length){var r=this._generatedMappings[e+1];if(t.generatedLine===r.generatedLine){t.lastGeneratedColumn=r.generatedColumn-1;continue}}t.lastGeneratedColumn=1/0}},Xe.prototype.originalPositionFor=function(e){var t={generatedLine:qe.getArg(e,"line"),generatedColumn:qe.getArg(e,"column")},r=this._findMapping(t,this._generatedMappings,"generatedLine","generatedColumn",qe.compareByGeneratedPositionsDeflated,qe.getArg(e,"bias",Ke.GREATEST_LOWER_BOUND));if(r>=0){var n=this._generatedMappings[r];if(n.generatedLine===t.generatedLine){var s=qe.getArg(n,"source",null);null!==s&&(s=this._sources.at(s),s=qe.computeSourceURL(this.sourceRoot,s,this._sourceMapURL));var i=qe.getArg(n,"name",null);return null!==i&&(i=this._names.at(i)),{source:s,line:qe.getArg(n,"originalLine",null),column:qe.getArg(n,"originalColumn",null),name:i}}}return{source:null,line:null,column:null,name:null}},Xe.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&(this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some((function(e){return null==e})))},Xe.prototype.sourceContentFor=function(e,t){if(!this.sourcesContent)return null;var r=this._findSourceIndex(e);if(r>=0)return this.sourcesContent[r];var n,s=e;if(null!=this.sourceRoot&&(s=qe.relative(this.sourceRoot,s)),null!=this.sourceRoot&&(n=qe.urlParse(this.sourceRoot))){var i=s.replace(/^file:\/\//,"");if("file"==n.scheme&&this._sources.has(i))return this.sourcesContent[this._sources.indexOf(i)];if((!n.path||"/"==n.path)&&this._sources.has("/"+s))return this.sourcesContent[this._sources.indexOf("/"+s)]}if(t)return null;throw new Error('"'+s+'" is not in the SourceMap.')},Xe.prototype.generatedPositionFor=function(e){var t=qe.getArg(e,"source");if((t=this._findSourceIndex(t))<0)return{line:null,column:null,lastColumn:null};var r={source:t,originalLine:qe.getArg(e,"line"),originalColumn:qe.getArg(e,"column")},n=this._findMapping(r,this._originalMappings,"originalLine","originalColumn",qe.compareByOriginalPositions,qe.getArg(e,"bias",Ke.GREATEST_LOWER_BOUND));if(n>=0){var s=this._originalMappings[n];if(s.source===r.source)return{line:qe.getArg(s,"generatedLine",null),column:qe.getArg(s,"generatedColumn",null),lastColumn:qe.getArg(s,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},Ge.BasicSourceMapConsumer=Xe,nt.prototype=Object.create(Ke.prototype),nt.prototype.constructor=Ke,nt.prototype._version=3,Object.defineProperty(nt.prototype,"sources",{get:function(){for(var e=[],t=0;t<this._sections.length;t++)for(var r=0;r<this._sections[t].consumer.sources.length;r++)e.push(this._sections[t].consumer.sources[r]);return e}}),nt.prototype.originalPositionFor=function(e){var t={generatedLine:qe.getArg(e,"line"),generatedColumn:qe.getArg(e,"column")},r=Qe.search(t,this._sections,(function(e,t){var r=e.generatedLine-t.generatedOffset.generatedLine;return r||e.generatedColumn-t.generatedOffset.generatedColumn})),n=this._sections[r];return n?n.consumer.originalPositionFor({line:t.generatedLine-(n.generatedOffset.generatedLine-1),column:t.generatedColumn-(n.generatedOffset.generatedLine===t.generatedLine?n.generatedOffset.generatedColumn-1:0),bias:e.bias}):{source:null,line:null,column:null,name:null}},nt.prototype.hasContentsOfAllSources=function(){return this._sections.every((function(e){return e.consumer.hasContentsOfAllSources()}))},nt.prototype.sourceContentFor=function(e,t){for(var r=0;r<this._sections.length;r++){var n=this._sections[r].consumer.sourceContentFor(e,!0);if(n)return n}if(t)return null;throw new Error('"'+e+'" is not in the SourceMap.')},nt.prototype.generatedPositionFor=function(e){for(var t=0;t<this._sections.length;t++){var r=this._sections[t];if(-1!==r.consumer._findSourceIndex(qe.getArg(e,"source"))){var n=r.consumer.generatedPositionFor(e);if(n)return{line:n.line+(r.generatedOffset.generatedLine-1),column:n.column+(r.generatedOffset.generatedLine===n.line?r.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},nt.prototype._parseMappings=function(e,t){this.__generatedMappings=[],this.__originalMappings=[];for(var r=0;r<this._sections.length;r++)for(var n=this._sections[r],s=n.consumer._generatedMappings,i=0;i<s.length;i++){var o=s[i],a=n.consumer._sources.at(o.source);a=qe.computeSourceURL(n.consumer.sourceRoot,a,this._sourceMapURL),this._sources.add(a),a=this._sources.indexOf(a);var l=null;o.name&&(l=n.consumer._names.at(o.name),this._names.add(l),l=this._names.indexOf(l));var u={source:a,generatedLine:o.generatedLine+(n.generatedOffset.generatedLine-1),generatedColumn:o.generatedColumn+(n.generatedOffset.generatedLine===o.generatedLine?n.generatedOffset.generatedColumn-1:0),originalLine:o.originalLine,originalColumn:o.originalColumn,name:l};this.__generatedMappings.push(u),"number"==typeof u.originalLine&&this.__originalMappings.push(u)}He(this.__generatedMappings,qe.compareByGeneratedPositionsDeflated),He(this.__originalMappings,qe.compareByOriginalPositions)},Ge.IndexedSourceMapConsumer=nt;var st={},it=_e.SourceMapGenerator,ot=ke,at=/(\r?\n)/,lt="$$$isSourceNode$$$";function ut(e,t,r,n,s){this.children=[],this.sourceContents={},this.line=null==e?null:e,this.column=null==t?null:t,this.source=null==r?null:r,this.name=null==s?null:s,this[lt]=!0,null!=n&&this.add(n)}ut.fromStringWithSourceMap=function(e,t,r){var n=new ut,s=e.split(at),i=0,o=function(){return e()+(e()||"");function e(){return i<s.length?s[i++]:void 0}},a=1,l=0,u=null;return t.eachMapping((function(e){if(null!==u){if(!(a<e.generatedLine)){var t=(r=s[i]||"").substr(0,e.generatedColumn-l);return s[i]=r.substr(e.generatedColumn-l),l=e.generatedColumn,c(u,t),void(u=e)}c(u,o()),a++,l=0}for(;a<e.generatedLine;)n.add(o()),a++;if(l<e.generatedColumn){var r=s[i]||"";n.add(r.substr(0,e.generatedColumn)),s[i]=r.substr(e.generatedColumn),l=e.generatedColumn}u=e}),this),i<s.length&&(u&&c(u,o()),n.add(s.splice(i).join(""))),t.sources.forEach((function(e){var s=t.sourceContentFor(e);null!=s&&(null!=r&&(e=ot.join(r,e)),n.setSourceContent(e,s))})),n;function c(e,t){if(null===e||void 0===e.source)n.add(t);else{var s=r?ot.join(r,e.source):e.source;n.add(new ut(e.originalLine,e.originalColumn,s,t,e.name))}}},ut.prototype.add=function(e){if(Array.isArray(e))e.forEach((function(e){this.add(e)}),this);else{if(!e[lt]&&"string"!=typeof e)throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);e&&this.children.push(e)}return this},ut.prototype.prepend=function(e){if(Array.isArray(e))for(var t=e.length-1;t>=0;t--)this.prepend(e[t]);else{if(!e[lt]&&"string"!=typeof e)throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);this.children.unshift(e)}return this},ut.prototype.walk=function(e){for(var t,r=0,n=this.children.length;r<n;r++)(t=this.children[r])[lt]?t.walk(e):""!==t&&e(t,{source:this.source,line:this.line,column:this.column,name:this.name})},ut.prototype.join=function(e){var t,r,n=this.children.length;if(n>0){for(t=[],r=0;r<n-1;r++)t.push(this.children[r]),t.push(e);t.push(this.children[r]),this.children=t}return this},ut.prototype.replaceRight=function(e,t){var r=this.children[this.children.length-1];return r[lt]?r.replaceRight(e,t):"string"==typeof r?this.children[this.children.length-1]=r.replace(e,t):this.children.push("".replace(e,t)),this},ut.prototype.setSourceContent=function(e,t){this.sourceContents[ot.toSetString(e)]=t},ut.prototype.walkSourceContents=function(e){for(var t=0,r=this.children.length;t<r;t++)this.children[t][lt]&&this.children[t].walkSourceContents(e);var n=Object.keys(this.sourceContents);for(t=0,r=n.length;t<r;t++)e(ot.fromSetString(n[t]),this.sourceContents[n[t]])},ut.prototype.toString=function(){var e="";return this.walk((function(t){e+=t})),e},ut.prototype.toStringWithSourceMap=function(e){var t={code:"",line:1,column:0},r=new it(e),n=!1,s=null,i=null,o=null,a=null;return this.walk((function(e,l){t.code+=e,null!==l.source&&null!==l.line&&null!==l.column?(s===l.source&&i===l.line&&o===l.column&&a===l.name||r.addMapping({source:l.source,original:{line:l.line,column:l.column},generated:{line:t.line,column:t.column},name:l.name}),s=l.source,i=l.line,o=l.column,a=l.name,n=!0):n&&(r.addMapping({generated:{line:t.line,column:t.column}}),s=null,n=!1);for(var u=0,c=e.length;u<c;u++)10===e.charCodeAt(u)?(t.line++,t.column=0,u+1===c?(s=null,n=!1):n&&r.addMapping({source:l.source,original:{line:l.line,column:l.column},generated:{line:t.line,column:t.column},name:l.name})):t.column++})),this.walkSourceContents((function(e,t){r.setSourceContent(e,t)})),{code:t.code,map:r}},st.SourceNode=ut,be.SourceMapGenerator=_e.SourceMapGenerator,be.SourceMapConsumer=Ge.SourceMapConsumer,be.SourceNode=st.SourceNode;var ct={nanoid:(e=21)=>{let t="",r=e;for(;r--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[64*Math.random()|0];return t},customAlphabet:(e,t)=>()=>{let r="",n=t;for(;n--;)r+=e[Math.random()*e.length|0];return r}};let{SourceMapConsumer:ht,SourceMapGenerator:pt}=be,{existsSync:ft,readFileSync:dt}=l.default,{dirname:gt,join:mt}=o.default;class wt{constructor(e,t){if(!1===t.map)return;this.loadAnnotation(e),this.inline=this.startWith(this.annotation,"data:");let r=t.map?t.map.prev:void 0,n=this.loadMap(t.from,r);!this.mapFile&&t.from&&(this.mapFile=t.from),this.mapFile&&(this.root=gt(this.mapFile)),n&&(this.text=n)}consumer(){return this.consumerCache||(this.consumerCache=new ht(this.text)),this.consumerCache}withContent(){return!!(this.consumer().sourcesContent&&this.consumer().sourcesContent.length>0)}startWith(e,t){return!!e&&e.substr(0,t.length)===t}getAnnotationURL(e){return e.replace(/^\/\*\s*# sourceMappingURL=/,"").trim()}loadAnnotation(e){let t=e.match(/\/\*\s*# sourceMappingURL=/gm);if(!t)return;let r=e.lastIndexOf(t.pop()),n=e.indexOf("*/",r);r>-1&&n>-1&&(this.annotation=this.getAnnotationURL(e.substring(r,n)))}decodeInline(e){if(/^data:application\/json;charset=utf-?8,/.test(e)||/^data:application\/json,/.test(e))return decodeURIComponent(e.substr(RegExp.lastMatch.length));if(/^data:application\/json;charset=utf-?8;base64,/.test(e)||/^data:application\/json;base64,/.test(e))return t=e.substr(RegExp.lastMatch.length),Buffer?Buffer.from(t,"base64").toString():window.atob(t);var t;let r=e.match(/data:application\/json;([^,]+),/)[1];throw new Error("Unsupported source map encoding "+r)}loadFile(e){if(this.root=gt(e),ft(e))return this.mapFile=e,dt(e,"utf-8").toString().trim()}loadMap(e,t){if(!1===t)return!1;if(t){if("string"==typeof t)return t;if("function"!=typeof t){if(t instanceof ht)return pt.fromSourceMap(t).toString();if(t instanceof pt)return t.toString();if(this.isMap(t))return JSON.stringify(t);throw new Error("Unsupported previous source map format: "+t.toString())}{let r=t(e);if(r){let e=this.loadFile(r);if(!e)throw new Error("Unable to load previous source map: "+r.toString());return e}}}else{if(this.inline)return this.decodeInline(this.annotation);if(this.annotation){let t=this.annotation;return e&&(t=mt(gt(e),t)),this.loadFile(t)}}}isMap(e){return"object"==typeof e&&("string"==typeof e.mappings||"string"==typeof e._mappings||Array.isArray(e.sections))}}var yt=wt;wt.default=wt;let{SourceMapConsumer:vt,SourceMapGenerator:Ct}=be,{fileURLToPath:St,pathToFileURL:bt}=a.default,{resolve:_t,isAbsolute:xt}=o.default,{nanoid:Ot}=ct,At=X,Mt=ne,kt=yt,Et=Symbol("fromOffsetCache"),Lt=Boolean(vt&&Ct),Rt=Boolean(_t&&xt);class Pt{constructor(e,t={}){if(null==e||"object"==typeof e&&!e.toString)throw new Error(`PostCSS received ${e} instead of CSS string`);if(this.css=e.toString(),"\ufeff"===this.css[0]||"￾"===this.css[0]?(this.hasBOM=!0,this.css=this.css.slice(1)):this.hasBOM=!1,t.from&&(!Rt||/^\w+:\/\//.test(t.from)||xt(t.from)?this.file=t.from:this.file=_t(t.from)),Rt&&Lt){let e=new kt(this.css,t);if(e.text){this.map=e;let t=e.consumer().file;!this.file&&t&&(this.file=this.mapResolve(t))}}this.file||(this.id="<input css "+Ot(6)+">"),this.map&&(this.map.file=this.from)}fromOffset(e){let t,r;if(this[Et])r=this[Et];else{let e=this.css.split("\n");r=new Array(e.length);let t=0;for(let n=0,s=e.length;n<s;n++)r[n]=t,t+=e[n].length+1;this[Et]=r}t=r[r.length-1];let n=0;if(e>=t)n=r.length-1;else{let t,s=r.length-2;for(;n<s;)if(t=n+(s-n>>1),e<r[t])s=t-1;else{if(!(e>=r[t+1])){n=t;break}n=t+1}}return{line:n+1,col:e-r[n]+1}}error(e,t,r,n={}){let s,i,o;if(t&&"object"==typeof t){let e=t,n=r;if("number"==typeof t.offset){let n=this.fromOffset(e.offset);t=n.line,r=n.col}else t=e.line,r=e.column;if("number"==typeof n.offset){let e=this.fromOffset(n.offset);i=e.line,o=e.col}else i=n.line,o=n.column}else if(!r){let e=this.fromOffset(t);t=e.line,r=e.col}let a=this.origin(t,r,i,o);return s=a?new Mt(e,void 0===a.endLine?a.line:{line:a.line,column:a.column},void 0===a.endLine?a.column:{line:a.endLine,column:a.endColumn},a.source,a.file,n.plugin):new Mt(e,void 0===i?t:{line:t,column:r},void 0===i?r:{line:i,column:o},this.css,this.file,n.plugin),s.input={line:t,column:r,endLine:i,endColumn:o,source:this.css},this.file&&(bt&&(s.input.url=bt(this.file).toString()),s.input.file=this.file),s}origin(e,t,r,n){if(!this.map)return!1;let s,i,o=this.map.consumer(),a=o.originalPositionFor({line:e,column:t});if(!a.source)return!1;"number"==typeof r&&(s=o.originalPositionFor({line:r,column:n})),i=xt(a.source)?bt(a.source):new URL(a.source,this.map.consumer().sourceRoot||bt(this.map.mapFile));let l={url:i.toString(),line:a.line,column:a.column,endLine:s&&s.line,endColumn:s&&s.column};if("file:"===i.protocol){if(!St)throw new Error("file: protocol is not available in this PostCSS build");l.file=St(i)}let u=o.sourceContentFor(a.source);return u&&(l.source=u),l}mapResolve(e){return/^\w+:\/\//.test(e)?e:_t(this.map.consumer().sourceRoot||this.map.root||".",e)}get from(){return this.file||this.id}toJSON(){let e={};for(let t of["hasBOM","css","file","id"])null!=this[t]&&(e[t]=this[t]);return this.map&&(e.map={...this.map},e.map.consumerCache&&(e.map.consumerCache=void 0)),e}}var It=Pt;Pt.default=Pt,At&&At.registerInput&&At.registerInput(Pt);let{SourceMapConsumer:jt,SourceMapGenerator:Nt}=be,{dirname:Ut,resolve:Bt,relative:Dt,sep:Ft}=o.default,{pathToFileURL:Tt}=a.default,$t=It,Gt=Boolean(jt&&Nt),zt=Boolean(Ut&&Bt&&Dt&&Ft);var Wt=class{constructor(e,t,r,n){this.stringify=e,this.mapOpts=r.map||{},this.root=t,this.opts=r,this.css=n}isMap(){return void 0!==this.opts.map?!!this.opts.map:this.previous().length>0}previous(){if(!this.previousMaps)if(this.previousMaps=[],this.root)this.root.walk((e=>{if(e.source&&e.source.input.map){let t=e.source.input.map;this.previousMaps.includes(t)||this.previousMaps.push(t)}}));else{let e=new $t(this.css,this.opts);e.map&&this.previousMaps.push(e.map)}return this.previousMaps}isInline(){if(void 0!==this.mapOpts.inline)return this.mapOpts.inline;let e=this.mapOpts.annotation;return(void 0===e||!0===e)&&(!this.previous().length||this.previous().some((e=>e.inline)))}isSourcesContent(){return void 0!==this.mapOpts.sourcesContent?this.mapOpts.sourcesContent:!this.previous().length||this.previous().some((e=>e.withContent()))}clearAnnotation(){if(!1!==this.mapOpts.annotation)if(this.root){let e;for(let t=this.root.nodes.length-1;t>=0;t--)e=this.root.nodes[t],"comment"===e.type&&0===e.text.indexOf("# sourceMappingURL=")&&this.root.removeChild(t)}else this.css&&(this.css=this.css.replace(/(\n)?\/\*#[\S\s]*?\*\/$/gm,""))}setSourcesContent(){let e={};if(this.root)this.root.walk((t=>{if(t.source){let r=t.source.input.from;r&&!e[r]&&(e[r]=!0,this.map.setSourceContent(this.toUrl(this.path(r)),t.source.input.css))}}));else if(this.css){let e=this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>";this.map.setSourceContent(e,this.css)}}applyPrevMaps(){for(let e of this.previous()){let t,r=this.toUrl(this.path(e.file)),n=e.root||Ut(e.file);!1===this.mapOpts.sourcesContent?(t=new jt(e.text),t.sourcesContent&&(t.sourcesContent=t.sourcesContent.map((()=>null)))):t=e.consumer(),this.map.applySourceMap(t,r,this.toUrl(this.path(n)))}}isAnnotation(){return!!this.isInline()||(void 0!==this.mapOpts.annotation?this.mapOpts.annotation:!this.previous().length||this.previous().some((e=>e.annotation)))}toBase64(e){return Buffer?Buffer.from(e).toString("base64"):window.btoa(unescape(encodeURIComponent(e)))}addAnnotation(){let e;e=this.isInline()?"data:application/json;base64,"+this.toBase64(this.map.toString()):"string"==typeof this.mapOpts.annotation?this.mapOpts.annotation:"function"==typeof this.mapOpts.annotation?this.mapOpts.annotation(this.opts.to,this.root):this.outputFile()+".map";let t="\n";this.css.includes("\r\n")&&(t="\r\n"),this.css+=t+"/*# sourceMappingURL="+e+" */"}outputFile(){return this.opts.to?this.path(this.opts.to):this.opts.from?this.path(this.opts.from):"to.css"}generateMap(){if(this.root)this.generateString();else if(1===this.previous().length){let e=this.previous()[0].consumer();e.file=this.outputFile(),this.map=Nt.fromSourceMap(e)}else this.map=new Nt({file:this.outputFile()}),this.map.addMapping({source:this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>",generated:{line:1,column:0},original:{line:1,column:0}});return this.isSourcesContent()&&this.setSourcesContent(),this.root&&this.previous().length>0&&this.applyPrevMaps(),this.isAnnotation()&&this.addAnnotation(),this.isInline()?[this.css]:[this.css,this.map]}path(e){if(0===e.indexOf("<"))return e;if(/^\w+:\/\//.test(e))return e;if(this.mapOpts.absolute)return e;let t=this.opts.to?Ut(this.opts.to):".";return"string"==typeof this.mapOpts.annotation&&(t=Ut(Bt(t,this.mapOpts.annotation))),e=Dt(t,e)}toUrl(e){return"\\"===Ft&&(e=e.replace(/\\/g,"/")),encodeURI(e).replace(/[#?]/g,encodeURIComponent)}sourcePath(e){if(this.mapOpts.from)return this.toUrl(this.mapOpts.from);if(this.mapOpts.absolute){if(Tt)return Tt(e.source.input.from).toString();throw new Error("`map.absolute` option is not available in this PostCSS build")}return this.toUrl(this.path(e.source.input.from))}generateString(){this.css="",this.map=new Nt({file:this.outputFile()});let e,t,r=1,n=1,s="<no source>",i={source:"",generated:{line:0,column:0},original:{line:0,column:0}};this.stringify(this.root,((o,a,l)=>{if(this.css+=o,a&&"end"!==l&&(i.generated.line=r,i.generated.column=n-1,a.source&&a.source.start?(i.source=this.sourcePath(a),i.original.line=a.source.start.line,i.original.column=a.source.start.column-1,this.map.addMapping(i)):(i.source=s,i.original.line=1,i.original.column=0,this.map.addMapping(i))),e=o.match(/\n/g),e?(r+=e.length,t=o.lastIndexOf("\n"),n=o.length-t):n+=o.length,a&&"start"!==l){let e=a.parent||{raws:{}};("decl"!==a.type||a!==e.last||e.raws.semicolon)&&(a.source&&a.source.end?(i.source=this.sourcePath(a),i.original.line=a.source.end.line,i.original.column=a.source.end.column-1,i.generated.line=r,i.generated.column=n-2,this.map.addMapping(i)):(i.source=s,i.original.line=1,i.original.column=0,i.generated.line=r,i.generated.column=n-1,this.map.addMapping(i)))}}))}generate(){if(this.clearAnnotation(),zt&&Gt&&this.isMap())return this.generateMap();{let e="";return this.stringify(this.root,(t=>{e+=t})),[e]}}};let Vt=ye;class Jt extends Vt{constructor(e){super(e),this.type="comment"}}var qt=Jt;Jt.default=Jt;let Qt,Yt,Zt,{isClean:Ht,my:Kt}=se,Xt=Se,er=qt,tr=ye;function rr(e){return e.map((e=>(e.nodes&&(e.nodes=rr(e.nodes)),delete e.source,e)))}function nr(e){if(e[Ht]=!1,e.proxyOf.nodes)for(let t of e.proxyOf.nodes)nr(t)}class sr extends tr{push(e){return e.parent=this,this.proxyOf.nodes.push(e),this}each(e){if(!this.proxyOf.nodes)return;let t,r,n=this.getIterator();for(;this.indexes[n]<this.proxyOf.nodes.length&&(t=this.indexes[n],r=e(this.proxyOf.nodes[t],t),!1!==r);)this.indexes[n]+=1;return delete this.indexes[n],r}walk(e){return this.each(((t,r)=>{let n;try{n=e(t,r)}catch(e){throw t.addToError(e)}return!1!==n&&t.walk&&(n=t.walk(e)),n}))}walkDecls(e,t){return t?e instanceof RegExp?this.walk(((r,n)=>{if("decl"===r.type&&e.test(r.prop))return t(r,n)})):this.walk(((r,n)=>{if("decl"===r.type&&r.prop===e)return t(r,n)})):(t=e,this.walk(((e,r)=>{if("decl"===e.type)return t(e,r)})))}walkRules(e,t){return t?e instanceof RegExp?this.walk(((r,n)=>{if("rule"===r.type&&e.test(r.selector))return t(r,n)})):this.walk(((r,n)=>{if("rule"===r.type&&r.selector===e)return t(r,n)})):(t=e,this.walk(((e,r)=>{if("rule"===e.type)return t(e,r)})))}walkAtRules(e,t){return t?e instanceof RegExp?this.walk(((r,n)=>{if("atrule"===r.type&&e.test(r.name))return t(r,n)})):this.walk(((r,n)=>{if("atrule"===r.type&&r.name===e)return t(r,n)})):(t=e,this.walk(((e,r)=>{if("atrule"===e.type)return t(e,r)})))}walkComments(e){return this.walk(((t,r)=>{if("comment"===t.type)return e(t,r)}))}append(...e){for(let t of e){let e=this.normalize(t,this.last);for(let t of e)this.proxyOf.nodes.push(t)}return this.markDirty(),this}prepend(...e){e=e.reverse();for(let t of e){let e=this.normalize(t,this.first,"prepend").reverse();for(let t of e)this.proxyOf.nodes.unshift(t);for(let t in this.indexes)this.indexes[t]=this.indexes[t]+e.length}return this.markDirty(),this}cleanRaws(e){if(super.cleanRaws(e),this.nodes)for(let t of this.nodes)t.cleanRaws(e)}insertBefore(e,t){let r,n=0===(e=this.index(e))&&"prepend",s=this.normalize(t,this.proxyOf.nodes[e],n).reverse();for(let t of s)this.proxyOf.nodes.splice(e,0,t);for(let t in this.indexes)r=this.indexes[t],e<=r&&(this.indexes[t]=r+s.length);return this.markDirty(),this}insertAfter(e,t){e=this.index(e);let r,n=this.normalize(t,this.proxyOf.nodes[e]).reverse();for(let t of n)this.proxyOf.nodes.splice(e+1,0,t);for(let t in this.indexes)r=this.indexes[t],e<r&&(this.indexes[t]=r+n.length);return this.markDirty(),this}removeChild(e){let t;e=this.index(e),this.proxyOf.nodes[e].parent=void 0,this.proxyOf.nodes.splice(e,1);for(let r in this.indexes)t=this.indexes[r],t>=e&&(this.indexes[r]=t-1);return this.markDirty(),this}removeAll(){for(let e of this.proxyOf.nodes)e.parent=void 0;return this.proxyOf.nodes=[],this.markDirty(),this}replaceValues(e,t,r){return r||(r=t,t={}),this.walkDecls((n=>{t.props&&!t.props.includes(n.prop)||t.fast&&!n.value.includes(t.fast)||(n.value=n.value.replace(e,r))})),this.markDirty(),this}every(e){return this.nodes.every(e)}some(e){return this.nodes.some(e)}index(e){return"number"==typeof e?e:(e.proxyOf&&(e=e.proxyOf),this.proxyOf.nodes.indexOf(e))}get first(){if(this.proxyOf.nodes)return this.proxyOf.nodes[0]}get last(){if(this.proxyOf.nodes)return this.proxyOf.nodes[this.proxyOf.nodes.length-1]}normalize(e,t){if("string"==typeof e)e=rr(Qt(e).nodes);else if(Array.isArray(e)){e=e.slice(0);for(let t of e)t.parent&&t.parent.removeChild(t,"ignore")}else if("root"===e.type&&"document"!==this.type){e=e.nodes.slice(0);for(let t of e)t.parent&&t.parent.removeChild(t,"ignore")}else if(e.type)e=[e];else if(e.prop){if(void 0===e.value)throw new Error("Value field is missed in node creation");"string"!=typeof e.value&&(e.value=String(e.value)),e=[new Xt(e)]}else if(e.selector)e=[new Yt(e)];else if(e.name)e=[new Zt(e)];else{if(!e.text)throw new Error("Unknown node type in node creation");e=[new er(e)]}return e.map((e=>(e[Kt]||sr.rebuild(e),(e=e.proxyOf).parent&&e.parent.removeChild(e),e[Ht]&&nr(e),void 0===e.raws.before&&t&&void 0!==t.raws.before&&(e.raws.before=t.raws.before.replace(/\S/g,"")),e.parent=this,e)))}getProxyProcessor(){return{set:(e,t,r)=>(e[t]===r||(e[t]=r,"name"!==t&&"params"!==t&&"selector"!==t||e.markDirty()),!0),get:(e,t)=>"proxyOf"===t?e:e[t]?"each"===t||"string"==typeof t&&t.startsWith("walk")?(...r)=>e[t](...r.map((e=>"function"==typeof e?(t,r)=>e(t.toProxy(),r):e))):"every"===t||"some"===t?r=>e[t](((e,...t)=>r(e.toProxy(),...t))):"root"===t?()=>e.root().toProxy():"nodes"===t?e.nodes.map((e=>e.toProxy())):"first"===t||"last"===t?e[t].toProxy():e[t]:e[t]}}getIterator(){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach+=1;let e=this.lastEach;return this.indexes[e]=0,e}}sr.registerParse=e=>{Qt=e},sr.registerRule=e=>{Yt=e},sr.registerAtRule=e=>{Zt=e};var ir=sr;sr.default=sr,sr.rebuild=e=>{"atrule"===e.type?Object.setPrototypeOf(e,Zt.prototype):"rule"===e.type?Object.setPrototypeOf(e,Yt.prototype):"decl"===e.type?Object.setPrototypeOf(e,Xt.prototype):"comment"===e.type&&Object.setPrototypeOf(e,er.prototype),e[Kt]=!0,e.nodes&&e.nodes.forEach((e=>{sr.rebuild(e)}))};let or,ar,lr=ir;class ur extends lr{constructor(e){super({type:"document",...e}),this.nodes||(this.nodes=[])}toResult(e={}){return new or(new ar,this,e).stringify()}}ur.registerLazyResult=e=>{or=e},ur.registerProcessor=e=>{ar=e};var cr=ur;ur.default=ur;let hr={};var pr=function(e){hr[e]||(hr[e]=!0,"undefined"!=typeof console&&console.warn&&console.warn(e))};class fr{constructor(e,t={}){if(this.type="warning",this.text=e,t.node&&t.node.source){let e=t.node.rangeBy(t);this.line=e.start.line,this.column=e.start.column,this.endLine=e.end.line,this.endColumn=e.end.column}for(let e in t)this[e]=t[e]}toString(){return this.node?this.node.error(this.text,{plugin:this.plugin,index:this.index,word:this.word}).message:this.plugin?this.plugin+": "+this.text:this.text}}var dr=fr;fr.default=fr;let gr=dr;class mr{constructor(e,t,r){this.processor=e,this.messages=[],this.root=t,this.opts=r,this.css=void 0,this.map=void 0}toString(){return this.css}warn(e,t={}){t.plugin||this.lastPlugin&&this.lastPlugin.postcssPlugin&&(t.plugin=this.lastPlugin.postcssPlugin);let r=new gr(e,t);return this.messages.push(r),r}warnings(){return this.messages.filter((e=>"warning"===e.type))}get content(){return this.css}}var wr=mr;mr.default=mr;let yr=ir;class vr extends yr{constructor(e){super(e),this.type="atrule"}append(...e){return this.proxyOf.nodes||(this.nodes=[]),super.append(...e)}prepend(...e){return this.proxyOf.nodes||(this.nodes=[]),super.prepend(...e)}}var Cr=vr;vr.default=vr,yr.registerAtRule(vr);let Sr,br,_r=ir;class xr extends _r{constructor(e){super(e),this.type="root",this.nodes||(this.nodes=[])}removeChild(e,t){let r=this.index(e);return!t&&0===r&&this.nodes.length>1&&(this.nodes[1].raws.before=this.nodes[r].raws.before),super.removeChild(e)}normalize(e,t,r){let n=super.normalize(e);if(t)if("prepend"===r)this.nodes.length>1?t.raws.before=this.nodes[1].raws.before:delete t.raws.before;else if(this.first!==t)for(let e of n)e.raws.before=t.raws.before;return n}toResult(e={}){return new Sr(new br,this,e).stringify()}}xr.registerLazyResult=e=>{Sr=e},xr.registerProcessor=e=>{br=e};var Or=xr;xr.default=xr;let Ar={split(e,t,r){let n=[],s="",i=!1,o=0,a=!1,l=!1;for(let r of e)l?l=!1:"\\"===r?l=!0:a?r===a&&(a=!1):'"'===r||"'"===r?a=r:"("===r?o+=1:")"===r?o>0&&(o-=1):0===o&&t.includes(r)&&(i=!0),i?(""!==s&&n.push(s.trim()),s="",i=!1):s+=r;return(r||""!==s)&&n.push(s.trim()),n},space:e=>Ar.split(e,[" ","\n","\t"]),comma:e=>Ar.split(e,[","],!0)};var Mr=Ar;Ar.default=Ar;let kr=ir,Er=Mr;class Lr extends kr{constructor(e){super(e),this.type="rule",this.nodes||(this.nodes=[])}get selectors(){return Er.comma(this.selector)}set selectors(e){let t=this.selector?this.selector.match(/,\s*/):null,r=t?t[0]:","+this.raw("between","beforeOpen");this.selector=e.join(r)}}var Rr=Lr;Lr.default=Lr,kr.registerRule(Lr);let Pr=Se,Ir=J,jr=qt,Nr=Cr,Ur=Or,Br=Rr;var Dr=class{constructor(e){this.input=e,this.root=new Ur,this.current=this.root,this.spaces="",this.semicolon=!1,this.customProperty=!1,this.createTokenizer(),this.root.source={input:e,start:{offset:0,line:1,column:1}}}createTokenizer(){this.tokenizer=Ir(this.input)}parse(){let e;for(;!this.tokenizer.endOfFile();)switch(e=this.tokenizer.nextToken(),e[0]){case"space":this.spaces+=e[1];break;case";":this.freeSemicolon(e);break;case"}":this.end(e);break;case"comment":this.comment(e);break;case"at-word":this.atrule(e);break;case"{":this.emptyRule(e);break;default:this.other(e)}this.endFile()}comment(e){let t=new jr;this.init(t,e[2]),t.source.end=this.getPosition(e[3]||e[2]);let r=e[1].slice(2,-2);if(/^\s*$/.test(r))t.text="",t.raws.left=r,t.raws.right="";else{let e=r.match(/^(\s*)([^]*\S)(\s*)$/);t.text=e[2],t.raws.left=e[1],t.raws.right=e[3]}}emptyRule(e){let t=new Br;this.init(t,e[2]),t.selector="",t.raws.between="",this.current=t}other(e){let t=!1,r=null,n=!1,s=null,i=[],o=e[1].startsWith("--"),a=[],l=e;for(;l;){if(r=l[0],a.push(l),"("===r||"["===r)s||(s=l),i.push("("===r?")":"]");else if(o&&n&&"{"===r)s||(s=l),i.push("}");else if(0===i.length){if(";"===r){if(n)return void this.decl(a,o);break}if("{"===r)return void this.rule(a);if("}"===r){this.tokenizer.back(a.pop()),t=!0;break}":"===r&&(n=!0)}else r===i[i.length-1]&&(i.pop(),0===i.length&&(s=null));l=this.tokenizer.nextToken()}if(this.tokenizer.endOfFile()&&(t=!0),i.length>0&&this.unclosedBracket(s),t&&n){for(;a.length&&(l=a[a.length-1][0],"space"===l||"comment"===l);)this.tokenizer.back(a.pop());this.decl(a,o)}else this.unknownWord(a)}rule(e){e.pop();let t=new Br;this.init(t,e[0][2]),t.raws.between=this.spacesAndCommentsFromEnd(e),this.raw(t,"selector",e),this.current=t}decl(e,t){let r=new Pr;this.init(r,e[0][2]);let n,s=e[e.length-1];for(";"===s[0]&&(this.semicolon=!0,e.pop()),r.source.end=this.getPosition(s[3]||s[2]);"word"!==e[0][0];)1===e.length&&this.unknownWord(e),r.raws.before+=e.shift()[1];for(r.source.start=this.getPosition(e[0][2]),r.prop="";e.length;){let t=e[0][0];if(":"===t||"space"===t||"comment"===t)break;r.prop+=e.shift()[1]}for(r.raws.between="";e.length;){if(n=e.shift(),":"===n[0]){r.raws.between+=n[1];break}"word"===n[0]&&/\w/.test(n[1])&&this.unknownWord([n]),r.raws.between+=n[1]}"_"!==r.prop[0]&&"*"!==r.prop[0]||(r.raws.before+=r.prop[0],r.prop=r.prop.slice(1));let i=this.spacesAndCommentsFromStart(e);this.precheckMissedSemicolon(e);for(let t=e.length-1;t>=0;t--){if(n=e[t],"!important"===n[1].toLowerCase()){r.important=!0;let n=this.stringFrom(e,t);n=this.spacesFromEnd(e)+n," !important"!==n&&(r.raws.important=n);break}if("important"===n[1].toLowerCase()){let n=e.slice(0),s="";for(let e=t;e>0;e--){let t=n[e][0];if(0===s.trim().indexOf("!")&&"space"!==t)break;s=n.pop()[1]+s}0===s.trim().indexOf("!")&&(r.important=!0,r.raws.important=s,e=n)}if("space"!==n[0]&&"comment"!==n[0])break}let o=e.some((e=>"space"!==e[0]&&"comment"!==e[0]));this.raw(r,"value",e),o?r.raws.between+=i:r.value=i+r.value,r.value.includes(":")&&!t&&this.checkMissedSemicolon(e)}atrule(e){let t,r,n,s=new Nr;s.name=e[1].slice(1),""===s.name&&this.unnamedAtrule(s,e),this.init(s,e[2]);let i=!1,o=!1,a=[],l=[];for(;!this.tokenizer.endOfFile();){if(t=(e=this.tokenizer.nextToken())[0],"("===t||"["===t?l.push("("===t?")":"]"):"{"===t&&l.length>0?l.push("}"):t===l[l.length-1]&&l.pop(),0===l.length){if(";"===t){s.source.end=this.getPosition(e[2]),this.semicolon=!0;break}if("{"===t){o=!0;break}if("}"===t){if(a.length>0){for(n=a.length-1,r=a[n];r&&"space"===r[0];)r=a[--n];r&&(s.source.end=this.getPosition(r[3]||r[2]))}this.end(e);break}a.push(e)}else a.push(e);if(this.tokenizer.endOfFile()){i=!0;break}}s.raws.between=this.spacesAndCommentsFromEnd(a),a.length?(s.raws.afterName=this.spacesAndCommentsFromStart(a),this.raw(s,"params",a),i&&(e=a[a.length-1],s.source.end=this.getPosition(e[3]||e[2]),this.spaces=s.raws.between,s.raws.between="")):(s.raws.afterName="",s.params=""),o&&(s.nodes=[],this.current=s)}end(e){this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.semicolon=!1,this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.spaces="",this.current.parent?(this.current.source.end=this.getPosition(e[2]),this.current=this.current.parent):this.unexpectedClose(e)}endFile(){this.current.parent&&this.unclosedBlock(),this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.current.raws.after=(this.current.raws.after||"")+this.spaces}freeSemicolon(e){if(this.spaces+=e[1],this.current.nodes){let e=this.current.nodes[this.current.nodes.length-1];e&&"rule"===e.type&&!e.raws.ownSemicolon&&(e.raws.ownSemicolon=this.spaces,this.spaces="")}}getPosition(e){let t=this.input.fromOffset(e);return{offset:e,line:t.line,column:t.col}}init(e,t){this.current.push(e),e.source={start:this.getPosition(t),input:this.input},e.raws.before=this.spaces,this.spaces="","comment"!==e.type&&(this.semicolon=!1)}raw(e,t,r){let n,s,i,o,a=r.length,l="",u=!0,c=/^([#.|])?(\w)+/i;for(let t=0;t<a;t+=1)n=r[t],s=n[0],"comment"!==s||"rule"!==e.type?"comment"===s||"space"===s&&t===a-1?u=!1:l+=n[1]:(o=r[t-1],i=r[t+1],"space"!==o[0]&&"space"!==i[0]&&c.test(o[1])&&c.test(i[1])?l+=n[1]:u=!1);if(!u){let n=r.reduce(((e,t)=>e+t[1]),"");e.raws[t]={value:l,raw:n}}e[t]=l}spacesAndCommentsFromEnd(e){let t,r="";for(;e.length&&(t=e[e.length-1][0],"space"===t||"comment"===t);)r=e.pop()[1]+r;return r}spacesAndCommentsFromStart(e){let t,r="";for(;e.length&&(t=e[0][0],"space"===t||"comment"===t);)r+=e.shift()[1];return r}spacesFromEnd(e){let t,r="";for(;e.length&&(t=e[e.length-1][0],"space"===t);)r=e.pop()[1]+r;return r}stringFrom(e,t){let r="";for(let n=t;n<e.length;n++)r+=e[n][1];return e.splice(t,e.length-t),r}colon(e){let t,r,n,s=0;for(let[i,o]of e.entries()){if(t=o,r=t[0],"("===r&&(s+=1),")"===r&&(s-=1),0===s&&":"===r){if(n){if("word"===n[0]&&"progid"===n[1])continue;return i}this.doubleColon(t)}n=t}return!1}unclosedBracket(e){throw this.input.error("Unclosed bracket",{offset:e[2]},{offset:e[2]+1})}unknownWord(e){throw this.input.error("Unknown word",{offset:e[0][2]},{offset:e[0][2]+e[0][1].length})}unexpectedClose(e){throw this.input.error("Unexpected }",{offset:e[2]},{offset:e[2]+1})}unclosedBlock(){let e=this.current.source.start;throw this.input.error("Unclosed block",e.line,e.column)}doubleColon(e){throw this.input.error("Double colon",{offset:e[2]},{offset:e[2]+e[1].length})}unnamedAtrule(e,t){throw this.input.error("At-rule without name",{offset:t[2]},{offset:t[2]+t[1].length})}precheckMissedSemicolon(){}checkMissedSemicolon(e){let t=this.colon(e);if(!1===t)return;let r,n=0;for(let s=t-1;s>=0&&(r=e[s],"space"===r[0]||(n+=1,2!==n));s--);throw this.input.error("Missed semicolon","word"===r[0]?r[3]+1:r[2])}};let Fr=ir,Tr=Dr,$r=It;function Gr(e,t){let r=new $r(e,t),n=new Tr(r);try{n.parse()}catch(e){throw"production"!==process.env.NODE_ENV&&"CssSyntaxError"===e.name&&t&&t.from&&(/\.scss$/i.test(t.from)?e.message+="\nYou tried to parse SCSS with the standard CSS parser; try again with the postcss-scss parser":/\.sass/i.test(t.from)?e.message+="\nYou tried to parse Sass with the standard CSS parser; try again with the postcss-sass parser":/\.less$/i.test(t.from)&&(e.message+="\nYou tried to parse Less with the standard CSS parser; try again with the postcss-less parser")),e}return n.root}var zr=Gr;Gr.default=Gr,Fr.registerParse(Gr);let{isClean:Wr,my:Vr}=se,Jr=Wt,qr=ce,Qr=ir,Yr=cr,Zr=pr,Hr=wr,Kr=zr,Xr=Or;const en={document:"Document",root:"Root",atrule:"AtRule",rule:"Rule",decl:"Declaration",comment:"Comment"},tn={postcssPlugin:!0,prepare:!0,Once:!0,Document:!0,Root:!0,Declaration:!0,Rule:!0,AtRule:!0,Comment:!0,DeclarationExit:!0,RuleExit:!0,AtRuleExit:!0,CommentExit:!0,RootExit:!0,DocumentExit:!0,OnceExit:!0},rn={postcssPlugin:!0,prepare:!0,Once:!0};function nn(e){return"object"==typeof e&&"function"==typeof e.then}function sn(e){let t=!1,r=en[e.type];return"decl"===e.type?t=e.prop.toLowerCase():"atrule"===e.type&&(t=e.name.toLowerCase()),t&&e.append?[r,r+"-"+t,0,r+"Exit",r+"Exit-"+t]:t?[r,r+"-"+t,r+"Exit",r+"Exit-"+t]:e.append?[r,0,r+"Exit"]:[r,r+"Exit"]}function on(e){let t;return t="document"===e.type?["Document",0,"DocumentExit"]:"root"===e.type?["Root",0,"RootExit"]:sn(e),{node:e,events:t,eventIndex:0,visitors:[],visitorIndex:0,iterator:0}}function an(e){return e[Wr]=!1,e.nodes&&e.nodes.forEach((e=>an(e))),e}let ln={};class un{constructor(e,t,r){let n;if(this.stringified=!1,this.processed=!1,"object"!=typeof t||null===t||"root"!==t.type&&"document"!==t.type)if(t instanceof un||t instanceof Hr)n=an(t.root),t.map&&(void 0===r.map&&(r.map={}),r.map.inline||(r.map.inline=!1),r.map.prev=t.map);else{let e=Kr;r.syntax&&(e=r.syntax.parse),r.parser&&(e=r.parser),e.parse&&(e=e.parse);try{n=e(t,r)}catch(e){this.processed=!0,this.error=e}n&&!n[Vr]&&Qr.rebuild(n)}else n=an(t);this.result=new Hr(e,n,r),this.helpers={...ln,result:this.result,postcss:ln},this.plugins=this.processor.plugins.map((e=>"object"==typeof e&&e.prepare?{...e,...e.prepare(this.result)}:e))}get[Symbol.toStringTag](){return"LazyResult"}get processor(){return this.result.processor}get opts(){return this.result.opts}get css(){return this.stringify().css}get content(){return this.stringify().content}get map(){return this.stringify().map}get root(){return this.sync().root}get messages(){return this.sync().messages}warnings(){return this.sync().warnings()}toString(){return this.css}then(e,t){return"production"!==process.env.NODE_ENV&&("from"in this.opts||Zr("Without `from` option PostCSS could generate wrong source map and will not find Browserslist config. Set it to CSS file path or to `undefined` to prevent this warning.")),this.async().then(e,t)}catch(e){return this.async().catch(e)}finally(e){return this.async().then(e,e)}async(){return this.error?Promise.reject(this.error):this.processed?Promise.resolve(this.result):(this.processing||(this.processing=this.runAsync()),this.processing)}sync(){if(this.error)throw this.error;if(this.processed)return this.result;if(this.processed=!0,this.processing)throw this.getAsyncError();for(let e of this.plugins){if(nn(this.runOnRoot(e)))throw this.getAsyncError()}if(this.prepareVisitors(),this.hasListener){let e=this.result.root;for(;!e[Wr];)e[Wr]=!0,this.walkSync(e);if(this.listeners.OnceExit)if("document"===e.type)for(let t of e.nodes)this.visitSync(this.listeners.OnceExit,t);else this.visitSync(this.listeners.OnceExit,e)}return this.result}stringify(){if(this.error)throw this.error;if(this.stringified)return this.result;this.stringified=!0,this.sync();let e=this.result.opts,t=qr;e.syntax&&(t=e.syntax.stringify),e.stringifier&&(t=e.stringifier),t.stringify&&(t=t.stringify);let r=new Jr(t,this.result.root,this.result.opts).generate();return this.result.css=r[0],this.result.map=r[1],this.result}walkSync(e){e[Wr]=!0;let t=sn(e);for(let r of t)if(0===r)e.nodes&&e.each((e=>{e[Wr]||this.walkSync(e)}));else{let t=this.listeners[r];if(t&&this.visitSync(t,e.toProxy()))return}}visitSync(e,t){for(let[r,n]of e){let e;this.result.lastPlugin=r;try{e=n(t,this.helpers)}catch(e){throw this.handleError(e,t.proxyOf)}if("root"!==t.type&&"document"!==t.type&&!t.parent)return!0;if(nn(e))throw this.getAsyncError()}}runOnRoot(e){this.result.lastPlugin=e;try{if("object"==typeof e&&e.Once){if("document"===this.result.root.type){let t=this.result.root.nodes.map((t=>e.Once(t,this.helpers)));return nn(t[0])?Promise.all(t):t}return e.Once(this.result.root,this.helpers)}if("function"==typeof e)return e(this.result.root,this.result)}catch(e){throw this.handleError(e)}}getAsyncError(){throw new Error("Use process(css).then(cb) to work with async plugins")}handleError(e,t){let r=this.result.lastPlugin;try{if(t&&t.addToError(e),this.error=e,"CssSyntaxError"!==e.name||e.plugin){if(r.postcssVersion&&"production"!==process.env.NODE_ENV){let e=r.postcssPlugin,t=r.postcssVersion,n=this.result.processor.version,s=t.split("."),i=n.split(".");(s[0]!==i[0]||parseInt(s[1])>parseInt(i[1]))&&console.error("Unknown error from PostCSS plugin. Your current PostCSS version is "+n+", but "+e+" uses "+t+". Perhaps this is the source of the error below.")}}else e.plugin=r.postcssPlugin,e.setMessage()}catch(e){console&&console.error&&console.error(e)}return e}async runAsync(){this.plugin=0;for(let e=0;e<this.plugins.length;e++){let t=this.plugins[e],r=this.runOnRoot(t);if(nn(r))try{await r}catch(e){throw this.handleError(e)}}if(this.prepareVisitors(),this.hasListener){let e=this.result.root;for(;!e[Wr];){e[Wr]=!0;let t=[on(e)];for(;t.length>0;){let e=this.visitTick(t);if(nn(e))try{await e}catch(e){let r=t[t.length-1].node;throw this.handleError(e,r)}}}if(this.listeners.OnceExit)for(let[t,r]of this.listeners.OnceExit){this.result.lastPlugin=t;try{if("document"===e.type){let t=e.nodes.map((e=>r(e,this.helpers)));await Promise.all(t)}else await r(e,this.helpers)}catch(e){throw this.handleError(e)}}}return this.processed=!0,this.stringify()}prepareVisitors(){this.listeners={};let e=(e,t,r)=>{this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push([e,r])};for(let t of this.plugins)if("object"==typeof t)for(let r in t){if(!tn[r]&&/^[A-Z]/.test(r))throw new Error(`Unknown event ${r} in ${t.postcssPlugin}. Try to update PostCSS (${this.processor.version} now).`);if(!rn[r])if("object"==typeof t[r])for(let n in t[r])e(t,"*"===n?r:r+"-"+n.toLowerCase(),t[r][n]);else"function"==typeof t[r]&&e(t,r,t[r])}this.hasListener=Object.keys(this.listeners).length>0}visitTick(e){let t=e[e.length-1],{node:r,visitors:n}=t;if("root"!==r.type&&"document"!==r.type&&!r.parent)return void e.pop();if(n.length>0&&t.visitorIndex<n.length){let[e,s]=n[t.visitorIndex];t.visitorIndex+=1,t.visitorIndex===n.length&&(t.visitors=[],t.visitorIndex=0),this.result.lastPlugin=e;try{return s(r.toProxy(),this.helpers)}catch(e){throw this.handleError(e,r)}}if(0!==t.iterator){let n,s=t.iterator;for(;n=r.nodes[r.indexes[s]];)if(r.indexes[s]+=1,!n[Wr])return n[Wr]=!0,void e.push(on(n));t.iterator=0,delete r.indexes[s]}let s=t.events;for(;t.eventIndex<s.length;){let e=s[t.eventIndex];if(t.eventIndex+=1,0===e)return void(r.nodes&&r.nodes.length&&(r[Wr]=!0,t.iterator=r.getIterator()));if(this.listeners[e])return void(t.visitors=this.listeners[e])}e.pop()}}un.registerPostcss=e=>{ln=e};var cn=un;un.default=un,Xr.registerLazyResult(un),Yr.registerLazyResult(un);let hn=Wt,pn=ce,fn=pr,dn=zr;const gn=wr;class mn{constructor(e,t,r){let n;t=t.toString(),this.stringified=!1,this._processor=e,this._css=t,this._opts=r,this._map=void 0;let s=pn;this.result=new gn(this._processor,n,this._opts),this.result.css=t;let i=this;Object.defineProperty(this.result,"root",{get:()=>i.root});let o=new hn(s,n,this._opts,t);if(o.isMap()){let[e,t]=o.generate();e&&(this.result.css=e),t&&(this.result.map=t)}}get[Symbol.toStringTag](){return"NoWorkResult"}get processor(){return this.result.processor}get opts(){return this.result.opts}get css(){return this.result.css}get content(){return this.result.css}get map(){return this.result.map}get root(){if(this._root)return this._root;let e,t=dn;try{e=t(this._css,this._opts)}catch(e){this.error=e}return this._root=e,e}get messages(){return[]}warnings(){return[]}toString(){return this._css}then(e,t){return"production"!==process.env.NODE_ENV&&("from"in this._opts||fn("Without `from` option PostCSS could generate wrong source map and will not find Browserslist config. Set it to CSS file path or to `undefined` to prevent this warning.")),this.async().then(e,t)}catch(e){return this.async().catch(e)}finally(e){return this.async().then(e,e)}async(){return this.error?Promise.reject(this.error):Promise.resolve(this.result)}sync(){if(this.error)throw this.error;return this.result}}var wn=mn;mn.default=mn;let yn=wn,vn=cn,Cn=cr,Sn=Or;class bn{constructor(e=[]){this.version="8.4.5",this.plugins=this.normalize(e)}use(e){return this.plugins=this.plugins.concat(this.normalize([e])),this}process(e,t={}){return 0===this.plugins.length&&void 0===t.parser&&void 0===t.stringifier&&void 0===t.syntax?new yn(this,e,t):new vn(this,e,t)}normalize(e){let t=[];for(let r of e)if(!0===r.postcss?r=r():r.postcss&&(r=r.postcss),"object"==typeof r&&Array.isArray(r.plugins))t=t.concat(r.plugins);else if("object"==typeof r&&r.postcssPlugin)t.push(r);else if("function"==typeof r)t.push(r);else{if("object"!=typeof r||!r.parse&&!r.stringify)throw new Error(r+" is not a PostCSS plugin");if("production"!==process.env.NODE_ENV)throw new Error("PostCSS syntaxes cannot be used as plugins. Instead, please use one of the syntax/parser/stringifier options as outlined in your PostCSS runner documentation.")}return t}}var _n=bn;bn.default=bn,Sn.registerProcessor(bn),Cn.registerProcessor(bn);let xn=Se,On=yt,An=qt,Mn=Cr,kn=It,En=Or,Ln=Rr;function Rn(e,t){if(Array.isArray(e))return e.map((e=>Rn(e)));let{inputs:r,...n}=e;if(r){t=[];for(let e of r){let r={...e,__proto__:kn.prototype};r.map&&(r.map={...r.map,__proto__:On.prototype}),t.push(r)}}if(n.nodes&&(n.nodes=e.nodes.map((e=>Rn(e,t)))),n.source){let{inputId:e,...r}=n.source;n.source=r,null!=e&&(n.source.input=t[e])}if("root"===n.type)return new En(n);if("decl"===n.type)return new xn(n);if("rule"===n.type)return new Ln(n);if("comment"===n.type)return new An(n);if("atrule"===n.type)return new Mn(n);throw new Error("Unknown node type: "+e.type)}var Pn=Rn;Rn.default=Rn;let In=ne,jn=Se,Nn=cn,Un=ir,Bn=_n,Dn=ce,Fn=Pn,Tn=cr,$n=dr,Gn=qt,zn=Cr,Wn=wr,Vn=It,Jn=zr,qn=Mr,Qn=Rr,Yn=Or,Zn=ye;function Hn(...e){return 1===e.length&&Array.isArray(e[0])&&(e=e[0]),new Bn(e)}Hn.plugin=function(e,t){function r(...r){let n=t(...r);return n.postcssPlugin=e,n.postcssVersion=(new Bn).version,n}let n;return console&&console.warn&&(console.warn(e+": postcss.plugin was deprecated. Migration guide:\nhttps://evilmartians.com/chronicles/postcss-8-plugin-migration"),process.env.LANG&&process.env.LANG.startsWith("cn")&&console.warn(e+": 里面 postcss.plugin 被弃用. 迁移指南:\nhttps://www.w3ctech.com/topic/2226")),Object.defineProperty(r,"postcss",{get:()=>(n||(n=r()),n)}),r.process=function(e,t,n){return Hn([r(n)]).process(e,t)},r},Hn.stringify=Dn,Hn.parse=Jn,Hn.fromJSON=Fn,Hn.list=qn,Hn.comment=e=>new Gn(e),Hn.atRule=e=>new zn(e),Hn.decl=e=>new jn(e),Hn.rule=e=>new Qn(e),Hn.root=e=>new Yn(e),Hn.document=e=>new Tn(e),Hn.CssSyntaxError=In,Hn.Declaration=jn,Hn.Container=Un,Hn.Processor=Bn,Hn.Document=Tn,Hn.Comment=Gn,Hn.Warning=$n,Hn.AtRule=zn,Hn.Result=Wn,Hn.Input=Vn,Hn.Rule=Qn,Hn.Root=Yn,Hn.Node=Zn,Nn.registerPostcss(Hn);var Kn=Hn;async function Xn(){return new Promise((e=>{let t="",r=!1;if(setTimeout((()=>{r=!0,e("")}),1e4),process.stdin.isTTY){if(r)return;e(t)}else process.stdin.setEncoding("utf8"),process.stdin.on("readable",(()=>{let e;for(;e=process.stdin.read();)t+=e})),process.stdin.on("end",(()=>{r||e(t)}))}))}Hn.default=Hn,async function(e,t,r,s=!0){const i=function(e,t,r){const n=e.map((e=>e.trim())).filter((e=>!!e)),s={stdin:!1,stdout:!1,output:null,outputDir:null,inputs:[],inlineMap:!0,externalMap:!1,replace:!1,pluginOptions:{},debug:!1};let i=null,o=!1;for(let e=0;e<n.length;e++){const t=n[e];switch(t){case"-o":case"--output":s.output=n[e+1],e++,o=!0;break;case"-m":case"--map":s.externalMap=!0,s.inlineMap=!1,o=!0;break;case"--no-map":s.externalMap=!1,s.inlineMap=!1,o=!0;break;case"-r":case"--replace":s.replace=!0,o=!0;break;case"--debug":s.debug=!0,o=!0;break;case"-d":case"--dir":s.outputDir=n[e+1],e++,o=!0;break;case"-p":case"--plugin-options":i=n[e+1],e++,o=!0;break;default:if(0===t.indexOf("-"))return console.warn(`[error] unknown argument : ${t}\n`),r(),m.InvalidArguments;if(!o){s.inputs.push(t);break}return r(),m.InvalidArguments}}if(s.replace&&(s.output=null,s.outputDir=null),s.outputDir&&(s.output=null),s.inputs.length>1&&s.output)return console.warn('[error] omit "--output" when processing multiple inputs\n'),r(),m.InvalidArguments;0===s.inputs.length&&(s.stdin=!0),s.output||s.outputDir||s.replace||(s.stdout=!0),s.stdout&&(s.externalMap=!1);let a={};if(i)try{a=JSON.parse(i)}catch(e){return console.warn("[error] plugin options must be valid JSON\n"),r(),m.InvalidArguments}for(const e in a){const n=a[e];if(!t.includes(e))return console.warn(`[error] unknown plugin option: ${e}\n`),r(),m.InvalidArguments;s.pluginOptions[e]=n}return s}(process.argv.slice(s?2:3),t,r);i===m.InvalidArguments&&process.exit(1);const a=e(i.pluginOptions);i.stdin&&i.stdout?await async function(e,t,r){let n="";try{const s=await Xn();s||(r(),process.exit(1));const i=await Kn([e]).process(s,{from:"stdin",to:"stdout",map:!!t.inlineMap&&{inline:!0}});i.warnings().forEach((e=>{console.warn(e.toString())})),n=i.css}catch(e){console.error(t.debug?e:e.message),process.exit(1)}process.stdout.write(n+(t.inlineMap?"\n":"")),process.exit(0)}(a,i,r):i.stdin?await async function(e,t,r){let s=t.output;!s&&t.outputDir&&(s=o.default.join(t.outputDir,"output.css"));try{const i=await Xn();i||(r(),process.exit(1));const o=await Kn([e]).process(i,{from:"stdin",to:s,map:!(!t.inlineMap&&!t.externalMap)&&{inline:t.inlineMap}});o.warnings().forEach((e=>{console.warn(e.toString())})),t.externalMap&&o.map?await Promise.all([await n.promises.writeFile(s,o.css+(t.inlineMap?"\n":"")),await n.promises.writeFile(`${s}.map`,o.map.toString())]):await n.promises.writeFile(s,o.css+(t.inlineMap?"\n":""))}catch(e){console.error(t.debug?e:e.message),process.exit(1)}console.log(`CSS was written to "${o.default.normalize(s)}"`),process.exit(0)}(a,i,r):i.stdout?await async function(e,t){let r=[];try{r=await Promise.all(t.inputs.map((async t=>{const r=await n.promises.readFile(t),s=await Kn([e]).process(r,{from:t,to:"stdout",map:!1});return s.warnings().forEach((e=>{console.warn(e.toString())})),s.css})))}catch(e){console.error(t.debug?e:e.message),process.exit(1)}for(const e of r)process.stdout.write(e);process.exit(0)}(a,i):await async function(e,t){try{await Promise.all(t.inputs.map((async r=>{let s=t.output;t.outputDir&&(s=o.default.join(t.outputDir,o.default.basename(r))),t.replace&&(s=r);const i=await n.promises.readFile(r),a=await Kn([e]).process(i,{from:r,to:s,map:!(!t.inlineMap&&!t.externalMap)&&{inline:t.inlineMap}});a.warnings().forEach((e=>{console.warn(e.toString())})),t.externalMap&&a.map?await Promise.all([await n.promises.writeFile(s,a.css+(t.inlineMap?"\n":"")),await n.promises.writeFile(`${s}.map`,a.map.toString())]):await n.promises.writeFile(s,a.css+(t.inlineMap?"\n":"")),console.log(`CSS was written to "${o.default.normalize(s)}"`)})))}catch(e){console.error(t.debug?e:e.message),process.exit(1)}process.exit(0)}(a,i)}(g,["preserve","mediaQuery"],function(e,t,r,n=null){let s=[];if(n){const e=Math.max(...Object.keys(n).map((e=>e.length))),t=new Array(e).fill(" ").join("");t.length&&(s=["\nPlugin Options:",...Object.keys(n).map((e=>`  ${(e+t).slice(0,t.length)}  ${typeof n[e]}`))],s.push(`\n  ${JSON.stringify(n,null,2).split("\n").join("\n  ")}`))}const i=[`${t}\n`,`  ${r}\n`,"Usage:",`  ${e} [input.css] [OPTIONS] [-o|--output output.css]`,`  ${e} <input.css>... [OPTIONS] --dir <output-directory>`,`  ${e} <input.css>... [OPTIONS] --replace`,"\nOptions:","  -o, --output          Output file","  -d, --dir             Output directory","  -r, --replace         Replace (overwrite) the input file","  -m, --map             Create an external sourcemap","  --no-map              Disable the default inline sourcemaps","  -p, --plugin-options  Stringified JSON object with plugin options"];return s.length>0&&i.push(...s),()=>{console.warn(i.join("\n"))}}("css-prefers-color-scheme","Prefers Color Scheme","Lets you use light and dark color schemes in all browsers, following the [Media Queries] specification.",{preserve:!0,mediaQuery:"color|color-index"}));
