import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

// Common animation presets
export const fadeInUp = (element, options = {}) => {
  const defaults = {
    y: 50,
    opacity: 0,
    duration: 0.8,
    ease: "power2.out"
  };

  return gsap.fromTo(element, 
    { y: defaults.y, opacity: 0 },
    { ...defaults, ...options, y: 0, opacity: 1 }
  );
};

export const fadeInLeft = (element, options = {}) => {
  const defaults = {
    x: -50,
    opacity: 0,
    duration: 0.8,
    ease: "power2.out"
  };

  return gsap.fromTo(element,
    { x: defaults.x, opacity: 0 },
    { ...defaults, ...options, x: 0, opacity: 1 }
  );
};

export const fadeInRight = (element, options = {}) => {
  const defaults = {
    x: 50,
    opacity: 0,
    duration: 0.8,
    ease: "power2.out"
  };

  return gsap.fromTo(element,
    { x: defaults.x, opacity: 0 },
    { ...defaults, ...options, x: 0, opacity: 1 }
  );
};

export const scaleIn = (element, options = {}) => {
  const defaults = {
    scale: 0,
    opacity: 0,
    duration: 0.6,
    ease: "back.out(1.7)"
  };

  return gsap.fromTo(element,
    { scale: 0, opacity: 0 },
    { ...defaults, ...options, scale: 1, opacity: 1 }
  );
};

export const staggerAnimation = (elements, animationType = 'fadeInUp', options = {}) => {
  const defaults = {
    stagger: 0.1,
    delay: 0
  };

  const settings = { ...defaults, ...options };

  const animations = {
    fadeInUp: () => fadeInUp(elements, settings),
    fadeInLeft: () => fadeInLeft(elements, settings),
    fadeInRight: () => fadeInRight(elements, settings),
    scaleIn: () => scaleIn(elements, settings)
  };

  return animations[animationType] ? animations[animationType]() : fadeInUp(elements, settings);
};

export const scrollTriggerAnimation = (element, animation, options = {}) => {
  const defaults = {
    trigger: element,
    start: "top 80%",
    end: "bottom 20%",
    toggleActions: "play none none reverse"
  };

  return gsap.fromTo(element,
    animation.from,
    {
      ...animation.to,
      scrollTrigger: { ...defaults, ...options }
    }
  );
};

export const parallaxEffect = (element, options = {}) => {
  const defaults = {
    yPercent: -50,
    ease: "none"
  };

  return gsap.to(element, {
    ...defaults,
    ...options,
    scrollTrigger: {
      trigger: element,
      start: "top bottom",
      end: "bottom top",
      scrub: true,
      ...options.scrollTrigger
    }
  });
};

export const drawSVGPath = (path, options = {}) => {
  const defaults = {
    duration: 2,
    ease: "power2.inOut"
  };

  const length = path.getTotalLength();
  
  gsap.set(path, {
    strokeDasharray: length,
    strokeDashoffset: length
  });

  return gsap.to(path, {
    strokeDashoffset: 0,
    ...defaults,
    ...options
  });
};

export const morphSVG = (fromPath, toPath, options = {}) => {
  const defaults = {
    duration: 1,
    ease: "power2.inOut"
  };

  return gsap.to(fromPath, {
    morphSVG: toPath,
    ...defaults,
    ...options
  });
};

// Timeline helpers
export const createTimeline = (options = {}) => {
  return gsap.timeline(options);
};

export const addToTimeline = (timeline, animation, position) => {
  return timeline.add(animation, position);
};

// Cleanup utility
export const killAllScrollTriggers = () => {
  ScrollTrigger.getAll().forEach(trigger => trigger.kill());
};

export const refreshScrollTrigger = () => {
  ScrollTrigger.refresh();
};

// Mouse follow effect
export const mouseFollowEffect = (element, options = {}) => {
  const defaults = {
    ease: 0.1,
    scale: 1
  };

  const settings = { ...defaults, ...options };
  let mouse = { x: 0, y: 0 };
  let pos = { x: 0, y: 0 };

  const updatePosition = () => {
    pos.x += (mouse.x - pos.x) * settings.ease;
    pos.y += (mouse.y - pos.y) * settings.ease;
    
    gsap.set(element, {
      x: pos.x,
      y: pos.y,
      scale: settings.scale
    });
    
    requestAnimationFrame(updatePosition);
  };

  const handleMouseMove = (e) => {
    mouse.x = e.clientX;
    mouse.y = e.clientY;
  };

  document.addEventListener('mousemove', handleMouseMove);
  updatePosition();

  return () => {
    document.removeEventListener('mousemove', handleMouseMove);
  };
};
