import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { motion, useInView } from 'framer-motion';

const SVGAnimation = () => {
  const svgRef = useRef(null);
  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { once: true, threshold: 0.3 });

  useEffect(() => {
    if (isInView && svgRef.current) {
      const svg = svgRef.current;
      const paths = svg.querySelectorAll('path');
      const circles = svg.querySelectorAll('circle');

      // Reset all elements
      gsap.set([paths, circles], { opacity: 0, scale: 0 });

      // Create timeline
      const tl = gsap.timeline();

      // Animate paths with draw effect
      paths.forEach((path, index) => {
        const length = path.getTotalLength();
        gsap.set(path, {
          strokeDasharray: length,
          strokeDashoffset: length,
          opacity: 1,
          scale: 1
        });

        tl.to(path, {
          strokeDashoffset: 0,
          duration: 1.5,
          ease: "power2.inOut"
        }, index * 0.3);
      });

      // Animate circles
      tl.to(circles, {
        opacity: 1,
        scale: 1,
        duration: 0.8,
        stagger: 0.2,
        ease: "back.out(1.7)"
      }, "-=0.5");

      // Add floating animation
      tl.to(svg, {
        y: -10,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      });

      // Add rotation to specific elements
      gsap.to(svg.querySelector('#gear'), {
        rotation: 360,
        duration: 8,
        repeat: -1,
        ease: "none",
        transformOrigin: "center"
      });
    }
  }, [isInView]);

  const handleReplay = () => {
    if (svgRef.current) {
      const svg = svgRef.current;
      const paths = svg.querySelectorAll('path');
      const circles = svg.querySelectorAll('circle');

      // Reset and replay animation
      gsap.set([paths, circles], { opacity: 0, scale: 0 });
      
      paths.forEach((path) => {
        const length = path.getTotalLength();
        gsap.set(path, {
          strokeDasharray: length,
          strokeDashoffset: length,
          opacity: 1,
          scale: 1
        });
      });

      const tl = gsap.timeline();
      
      paths.forEach((path, index) => {
        tl.to(path, {
          strokeDashoffset: 0,
          duration: 1.5,
          ease: "power2.inOut"
        }, index * 0.3);
      });

      tl.to(circles, {
        opacity: 1,
        scale: 1,
        duration: 0.8,
        stagger: 0.2,
        ease: "back.out(1.7)"
      }, "-=0.5");
    }
  };

  return (
    <motion.div
      ref={containerRef}
      className="flex flex-col items-center space-y-8"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: isInView ? 1 : 0, y: isInView ? 0 : 50 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      <div className="relative">
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-full blur-xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <div className="relative bg-gray-800 rounded-2xl p-8 shadow-2xl">
          <svg
            ref={svgRef}
            width="300"
            height="300"
            viewBox="0 0 300 300"
            className="mx-auto"
          >
            {/* Background circle */}
            <circle
              cx="150"
              cy="150"
              r="140"
              fill="none"
              stroke="url(#gradient1)"
              strokeWidth="2"
              opacity="0.3"
            />

            {/* Main design paths */}
            <path
              d="M50 150 Q150 50 250 150 Q150 250 50 150"
              fill="none"
              stroke="url(#gradient2)"
              strokeWidth="3"
              strokeLinecap="round"
            />

            <path
              d="M150 50 L150 250 M50 150 L250 150"
              fill="none"
              stroke="url(#gradient3)"
              strokeWidth="2"
              strokeLinecap="round"
              opacity="0.7"
            />

            <path
              d="M100 100 Q150 75 200 100 Q225 150 200 200 Q150 225 100 200 Q75 150 100 100"
              fill="none"
              stroke="url(#gradient4)"
              strokeWidth="2"
              strokeLinecap="round"
            />

            {/* Animated circles */}
            <circle cx="150" cy="75" r="8" fill="url(#gradient2)" />
            <circle cx="225" cy="150" r="8" fill="url(#gradient3)" />
            <circle cx="150" cy="225" r="8" fill="url(#gradient4)" />
            <circle cx="75" cy="150" r="8" fill="url(#gradient1)" />

            {/* Central gear */}
            <g id="gear">
              <circle cx="150" cy="150" r="25" fill="url(#gradient5)" />
              <path
                d="M150 125 L160 135 L150 145 L140 135 Z M175 150 L165 160 L155 150 L165 140 Z M150 175 L140 165 L150 155 L160 165 Z M125 150 L135 140 L145 150 L135 160 Z"
                fill="#1f2937"
              />
            </g>

            {/* Gradients */}
            <defs>
              <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" />
                <stop offset="100%" stopColor="#8b5cf6" />
              </linearGradient>
              <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#10b981" />
                <stop offset="100%" stopColor="#06b6d4" />
              </linearGradient>
              <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#f59e0b" />
                <stop offset="100%" stopColor="#ef4444" />
              </linearGradient>
              <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8b5cf6" />
                <stop offset="100%" stopColor="#ec4899" />
              </linearGradient>
              <radialGradient id="gradient5">
                <stop offset="0%" stopColor="#60a5fa" />
                <stop offset="100%" stopColor="#3b82f6" />
              </radialGradient>
            </defs>
          </svg>
        </div>
      </div>

      <motion.div
        className="text-center max-w-md"
        initial={{ opacity: 0 }}
        animate={{ opacity: isInView ? 1 : 0 }}
        transition={{ delay: 0.5, duration: 0.6 }}
      >
        <h3 className="text-2xl font-bold mb-4 text-white">
          SVG Animations
        </h3>
        <p className="text-gray-300 leading-relaxed">
          Scalable vector graphics animated with GSAP for smooth, 
          performance-optimized animations that work on any screen size.
        </p>
      </motion.div>

      <motion.button
        onClick={handleReplay}
        className="px-8 py-3 bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 rounded-lg font-semibold transition-all duration-300"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: isInView ? 1 : 0, y: isInView ? 0 : 20 }}
        transition={{ delay: 0.8, duration: 0.6 }}
      >
        Replay Animation
      </motion.button>
    </motion.div>
  );
};

export default SVGAnimation;
